import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Search, Mail, Shield, Zap, Sparkles } from 'lucide-react';
import LoadingSpinner from './LoadingSpinner';

const schema = z.object({
  contentType: z.string().min(2, 'Content type is required'),
  searchLimit: z.enum(['limited', 'unlimited']),
  searchCount: z.coerce.number().min(1).max(50).optional(),
  blockedKeyword: z.string().optional(),
  email: z.string()
    .email('Valid email required')
    .refine((email) => email.endsWith('@gmail.com'), {
      message: 'Only Gmail accounts are accepted',
    }),
});

type FormValues = z.infer<typeof schema>;

interface AnalysisFormProps {
  onSubmit: (data: FormValues) => void;
  isLoading?: boolean;
}

const AnalysisForm: React.FC<AnalysisFormProps> = ({ onSubmit, isLoading }) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setError,
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      contentType: '',
      searchLimit: 'limited',
      searchCount: 10,
      blockedKeyword: '',
      email: '',
    },
  });

  const searchLimit = watch('searchLimit');

  const onFormSubmit = (data: FormValues) => {
    if (data.searchLimit === 'limited' && (!data.searchCount || data.searchCount < 1 || data.searchCount > 50)) {
      setError('searchCount', { message: 'Enter a value between 1 and 50' });
      return;
    }
    onSubmit(data);
    reset();
  };

  return (
    <div
      className="w-full max-w-lg mx-auto bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-2xl border border-yellow-500/30 shadow-2xl animate-fade-in relative overflow-hidden group"
      style={{
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 193, 7, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
      }}
    >
      {/* Subtle background pattern - stays fixed */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 via-transparent to-yellow-600/10"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,193,7,0.1),transparent_50%)]"></div>
      </div>

      {/* Animated Inner Content Layer */}
      <form
        onSubmit={handleSubmit(onFormSubmit)}
        className="relative z-10 p-6 flex flex-col gap-4 transition-all duration-500 ease-out group-hover:transform group-hover:-translate-y-1 group-hover:scale-[1.005]"
      >
        {/* Header Section */}
        <div className="transition-all duration-500 ease-out group-hover:transform group-hover:-translate-y-0.5">
          <h2 className="text-2xl font-black text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-500 mb-1 text-center tracking-tight">
            Start YouTube Analysis
          </h2>
          <p className="text-yellow-300/70 text-center text-xs mb-4">Discover trending content and analyze YouTube data</p>
        </div>

        {/* Form Fields Container - Animated Inner Content */}
        <div className="flex flex-col gap-6 transition-all duration-500 ease-out group-hover:transform group-hover:-translate-y-0.5">
          {/* Content Type */}
          <div className="animate-fade-in delay-100">
            <label className="flex flex-col gap-2">
              <span className="flex items-center gap-2 text-yellow-400 font-semibold text-sm">
                <div className="p-1.5 rounded-lg bg-yellow-400/10 border border-yellow-400/20 transition-all duration-300">
                  <Search className="w-4 h-4 text-yellow-400" />
                </div>
                <span>Content Type <span className="text-yellow-300">*</span></span>
              </span>
              <div className="relative mb-1">
                <input
                  {...register('contentType')}
                  type="text"
                  placeholder="e.g. Fitness Tutorials, Gaming Reviews, Cooking Shows"
                  className={`w-full px-3 py-2.5 bg-black/60 border-2 rounded-lg text-white placeholder:text-yellow-300/50 focus:ring-2 focus:ring-yellow-400/30 focus:border-yellow-400 transition-all duration-300 text-sm font-medium backdrop-blur-sm hover:bg-black/70 ${
                    errors.contentType ? 'border-red-400 focus:border-red-400 focus:ring-red-400/30' : 'border-yellow-500/30'
                  }`}
                  autoComplete="off"
                  disabled={isLoading}
                />
                {errors.contentType && (
                  <div className="mt-1 flex items-center gap-1 text-red-400 text-xs animate-pulse">
                    <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                    {errors.contentType.message}
                  </div>
                )}
              </div>
            </label>
          </div>
          {/* Search Limit */}
          <div className="animate-fade-in delay-200">
            <div className="flex flex-col gap-2">
              <span className="flex items-center gap-2 text-yellow-400 font-semibold text-sm">
                <div className="p-1.5 rounded-lg bg-yellow-400/10 border border-yellow-400/20 transition-all duration-300">
                  <Zap className="w-4 h-4 text-yellow-400" />
                </div>
                Search Limit
              </span>

              <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                <div className="flex gap-3">
                  <label className="flex items-center gap-2 cursor-pointer hover:bg-yellow-400/5 p-2 rounded-lg transition-all duration-200">
                    <div className="relative">
                      <input
                        type="radio"
                        value="limited"
                        {...register('searchLimit')}
                        checked={searchLimit === 'limited'}
                        disabled={isLoading}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${
                        searchLimit === 'limited'
                          ? 'border-yellow-400 bg-yellow-400'
                          : 'border-yellow-400/50 bg-transparent hover:border-yellow-400/70'
                      }`}>
                        {searchLimit === 'limited' && (
                          <div className="w-1.5 h-1.5 bg-black rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                        )}
                      </div>
                    </div>
                    <span className="text-white font-medium text-sm">Limited</span>
                  </label>

                  <label className="flex items-center gap-2 cursor-pointer hover:bg-yellow-400/5 p-2 rounded-lg transition-all duration-200">
                    <div className="relative">
                      <input
                        type="radio"
                        value="unlimited"
                        {...register('searchLimit')}
                        checked={searchLimit === 'unlimited'}
                        disabled={isLoading}
                        className="sr-only"
                      />
                      <div className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${
                        searchLimit === 'unlimited'
                          ? 'border-yellow-400 bg-yellow-400'
                          : 'border-yellow-400/50 bg-transparent hover:border-yellow-400/70'
                      }`}>
                        {searchLimit === 'unlimited' && (
                          <div className="w-1.5 h-1.5 bg-black rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                        )}
                      </div>
                    </div>
                    <span className="text-white font-medium text-sm">Unlimited</span>
                  </label>
                </div>

                <div className="relative mb-1">
                  <input
                    type="number"
                    min={1}
                    max={50}
                    {...register('searchCount', { valueAsNumber: true })}
                    className={`w-20 px-3 py-2 bg-black/60 border-2 rounded-lg text-white text-center font-medium focus:ring-2 focus:ring-yellow-400/30 focus:border-yellow-400 transition-all duration-300 backdrop-blur-sm text-sm ${
                      errors.searchCount ? 'border-red-400 focus:border-red-400 focus:ring-red-400/30' : 'border-yellow-500/30'
                    } ${searchLimit === 'unlimited' ? 'blur-sm opacity-40 pointer-events-none' : 'hover:bg-black/70'}`}
                    placeholder="10"
                    disabled={isLoading || searchLimit === 'unlimited'}
                  />
                  {errors.searchCount && (
                    <div className="mt-1 flex items-center gap-1 text-red-400 text-xs animate-pulse whitespace-nowrap">
                      <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                      {errors.searchCount.message}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* Blocked Keywords */}
          <div className="animate-fade-in delay-300">
            <label className="flex flex-col gap-2">
              <span className="flex items-center gap-2 text-yellow-400 font-semibold text-sm">
                <div className="p-1.5 rounded-lg bg-yellow-400/10 border border-yellow-400/20 transition-all duration-300">
                  <Shield className="w-4 h-4 text-yellow-400" />
                </div>
                <span>Blocked Keyword</span>
                <span className="text-xs text-yellow-300/70 font-normal">(Optional)</span>
              </span>
              <div className="relative">
                <input
                  {...register('blockedKeyword')}
                  type="text"
                  placeholder="e.g. prank, reaction, shorts, clickbait"
                  className="w-full px-3 py-2.5 bg-black/60 border-2 border-yellow-500/30 rounded-lg text-white placeholder:text-yellow-300/50 focus:ring-2 focus:ring-yellow-400/30 focus:border-yellow-400 transition-all duration-300 text-sm font-medium backdrop-blur-sm hover:bg-black/70"
                  autoComplete="off"
                  disabled={isLoading}
                />
              </div>
            </label>
          </div>

          {/* Email */}
          <div className="animate-fade-in delay-400">
            <label className="flex flex-col gap-2">
              <span className="flex items-center gap-2 text-yellow-400 font-semibold text-sm">
                <div className="p-1.5 rounded-lg bg-yellow-400/10 border border-yellow-400/20 transition-all duration-300">
                  <Mail className="w-4 h-4 text-yellow-400" />
                </div>
                <span>Email Address <span className="text-yellow-300">*</span></span>
                <span className="text-xs text-yellow-300/70 font-normal">(Must be Gmail)</span>
              </span>
              <div className="relative mb-1">
                <input
                  {...register('email')}
                  type="email"
                  placeholder="<EMAIL>"
                  className={`w-full px-3 py-2.5 bg-black/60 border-2 rounded-lg text-white placeholder:text-yellow-300/50 focus:ring-2 focus:ring-yellow-400/30 focus:border-yellow-400 transition-all duration-300 text-sm font-medium backdrop-blur-sm hover:bg-black/70 ${
                    errors.email ? 'border-red-400 focus:border-red-400 focus:ring-red-400/30' : 'border-yellow-500/30'
                  }`}
                  autoComplete="off"
                  disabled={isLoading}
                />
                {errors.email && (
                  <div className="mt-1 flex items-center gap-1 text-red-400 text-xs animate-pulse">
                    <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                    {errors.email.message}
                  </div>
                )}
              </div>
            </label>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-3 transition-all duration-500 ease-out group-hover:transform group-hover:-translate-y-0.5">
          <button
            type="submit"
            className="group/btn relative w-full flex items-center justify-center gap-2 text-lg font-bold px-6 py-3.5 rounded-xl bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-300 text-black shadow-2xl hover:shadow-yellow-400/25 focus:ring-4 focus:ring-yellow-400/50 focus:outline-none transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98] overflow-hidden"
            disabled={isLoading}
          >
            {/* Button background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-500 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>

            {/* Button content */}
            <div className="relative flex items-center gap-2">
              {isLoading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <div className="p-1 rounded-lg bg-black/10 group-hover/btn:bg-black/20 transition-all duration-300">
                  <Sparkles className="w-5 h-5 text-black group-hover/btn:scale-110 transition-transform duration-300" />
                </div>
              )}
              <span className="font-black tracking-wide">
                {isLoading ? 'Analyzing...' : 'Start Analysis'}
              </span>
            </div>

            {/* Shine effect */}
            <div className="absolute inset-0 -top-2 -bottom-2 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-1000 ease-out"></div>
          </button>

          <p className="text-yellow-300/60 text-center text-xs mt-3">
            Results will be sent to your email when analysis is complete
          </p>
        </div>
    </form>
    </div>
  );
};

export default AnalysisForm;