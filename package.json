{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && wrangler pages deploy dist", "pages:dev": "wrangler pages dev dist", "pages:deploy": "wrangler pages deploy dist --project-name=youtube-analytics"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@shadcn/ui": "^0.0.4", "js-cookie": "^3.0.5", "lucide-react": "^0.344.0", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "recharts": "^2.15.4", "vite-react-typescript-starter": "file:", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/js-cookie": "^3.0.6", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^3.2.4"}}