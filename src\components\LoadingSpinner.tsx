import React from 'react';

interface LoadingSpinnerProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ className = '', size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-5 h-5 border-2',
    md: 'w-8 h-8 border-3',
    lg: 'w-12 h-12 border-4'
  };

  const baseClass = sizeClasses[size];

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="relative">
        <div className={`${baseClass} rounded-full border-gray-200/30`}></div>
        <div className={`absolute top-0 left-0 ${baseClass} rounded-full border-transparent border-t-red-500 animate-spin`}></div>
      </div>
    </div>
  );
};

export default LoadingSpinner;