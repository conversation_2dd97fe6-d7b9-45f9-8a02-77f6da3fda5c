// Video data type
export type VideoData = {
  id: string;
  title: string;
  duration: number; // in seconds
  views: number;
  likes: number;
  comments: number;
  engagementRate: number; // (likes + comments) / views
  likeRate: number; // likes / views
  viewsPerHour: number;
  publishedAt: string; // ISO date
};

export type ContentFormat = 'Shorts' | 'Short-form' | 'Mid-form' | 'Long-form';

export interface FormatMetrics {
  count: number;
  avgViewsPerHour: number;
  avgEngagementRate: number;
  avgLikeRate: number;
  avgCommentRate: number;
  outliers: VideoData[];
}

export interface FormatTrends {
  [yearMonth: string]: {
    count: number;
    avgViewsPerHour: number;
    avgEngagementRate: number;
  };
}

export interface AnalysisResult {
  byFormat: Record<ContentFormat, FormatMetrics>;
  trends: Record<ContentFormat, FormatTrends>;
  recommendations: string[];
}

// 1. Segment videos by format
export function getContentFormat(duration: number): ContentFormat {
  if (duration <= 60) return 'Shorts';
  if (duration <= 300) return 'Short-form';
  if (duration <= 900) return 'Mid-form';
  return 'Long-form';
}

// 2. Calculate metrics per format
export function calculateFormatMetrics(videos: VideoData[]): Record<ContentFormat, FormatMetrics> {
  const formats: Record<ContentFormat, VideoData[]> = {
    'Shorts': [],
    'Short-form': [],
    'Mid-form': [],
    'Long-form': [],
  };
  videos.forEach(v => {
    formats[getContentFormat(v.duration)].push(v);
  });

  const result: Record<ContentFormat, FormatMetrics> = {
    'Shorts': getMetrics(formats['Shorts']),
    'Short-form': getMetrics(formats['Short-form']),
    'Mid-form': getMetrics(formats['Mid-form']),
    'Long-form': getMetrics(formats['Long-form']),
  };
  return result;
}

function getMetrics(videos: VideoData[]): FormatMetrics {
  const count = videos.length;
  if (count === 0) {
    return {
      count: 0,
      avgViewsPerHour: 0,
      avgEngagementRate: 0,
      avgLikeRate: 0,
      avgCommentRate: 0,
      outliers: [],
    };
  }
  const avgViewsPerHour = avg(videos.map(v => v.viewsPerHour));
  const avgEngagementRate = avg(videos.map(v => v.engagementRate));
  const avgLikeRate = avg(videos.map(v => v.likeRate));
  const avgCommentRate = avg(videos.map(v => v.comments / v.views));
  const outliers = findOutliers(videos, 'engagementRate');
  return {
    count,
    avgViewsPerHour,
    avgEngagementRate,
    avgLikeRate,
    avgCommentRate,
    outliers,
  };
}

function avg(arr: number[]): number {
  if (arr.length === 0) return 0;
  return arr.reduce((a, b) => a + b, 0) / arr.length;
}

// 3. Identify outliers (using 1.5*IQR rule)
function findOutliers(videos: VideoData[], key: keyof VideoData): VideoData[] {
  const values = videos.map(v => v[key] as number).sort((a, b) => a - b);
  if (values.length < 4) return [];
  const q1 = values[Math.floor(values.length / 4)];
  const q3 = values[Math.floor(values.length * 3 / 4)];
  const iqr = q3 - q1;
  const lower = q1 - 1.5 * iqr;
  const upper = q3 + 1.5 * iqr;
  return videos.filter(v => (v[key] as number) < lower || (v[key] as number) > upper);
}

// 4. Trends over time (by month)
export function calculateTrends(videos: VideoData[]): Record<ContentFormat, FormatTrends> {
  const byFormat: Record<ContentFormat, VideoData[]> = {
    'Shorts': [],
    'Short-form': [],
    'Mid-form': [],
    'Long-form': [],
  };
  videos.forEach(v => {
    byFormat[getContentFormat(v.duration)].push(v);
  });
  const result: Record<ContentFormat, FormatTrends> = {
    'Shorts': {},
    'Short-form': {},
    'Mid-form': {},
    'Long-form': {},
  };
  (Object.keys(byFormat) as ContentFormat[]).forEach(format => {
    byFormat[format].forEach(v => {
      const ym = v.publishedAt.slice(0, 7); // YYYY-MM
      if (!result[format][ym]) {
        result[format][ym] = { count: 0, avgViewsPerHour: 0, avgEngagementRate: 0 };
      }
      const group = result[format][ym];
      group.count++;
      group.avgViewsPerHour += v.viewsPerHour;
      group.avgEngagementRate += v.engagementRate;
    });
    Object.keys(result[format]).forEach(ym => {
      const group = result[format][ym];
      group.avgViewsPerHour /= group.count;
      group.avgEngagementRate /= group.count;
    });
  });
  return result;
}

// 5. Generate recommendations
export function generateRecommendations(metrics: Record<ContentFormat, FormatMetrics>, trends: Record<ContentFormat, FormatTrends>, industryBenchmarks?: Partial<Record<ContentFormat, { engagementRate: number }>>): string[] {
  const recs: string[] = [];
  // Example: Shorts outperforming
  if (metrics['Shorts'].avgEngagementRate > metrics['Mid-form'].avgEngagementRate * 1.5) {
    recs.push('Shorts drive significantly higher engagement than mid-form videos. Consider focusing on rapid-fire, short content.');
  }
  // Underperforming formats
  (Object.keys(metrics) as ContentFormat[]).forEach(format => {
    const bench = industryBenchmarks?.[format]?.engagementRate;
    if (bench && metrics[format].avgEngagementRate < bench * 0.8) {
      recs.push(`${format} engagement is below industry norms. Consider optimizing titles, thumbnails, or content pacing.`);
    }
    if (metrics[format].count > 0 && metrics[format].avgEngagementRate < 0.02) {
      recs.push(`${format} content has low engagement. Experiment with new formats or interactive elements.`);
    }
  });
  // Suggest shifting strategy
  if (metrics['Long-form'].count > 0 && metrics['Shorts'].avgViewsPerHour > metrics['Long-form'].avgViewsPerHour * 2) {
    recs.push('Convert 10% of long-form content into Shorts for higher reach and view velocity.');
  }
  return recs;
}

// 6. Main analysis function
export function analyzeContentPerformance(videos: VideoData[], industryBenchmarks?: Partial<Record<ContentFormat, { engagementRate: number }>>): AnalysisResult {
  const byFormat = calculateFormatMetrics(videos);
  const trends = calculateTrends(videos);
  const recommendations = generateRecommendations(byFormat, trends, industryBenchmarks);
  return { byFormat, trends, recommendations };
} 