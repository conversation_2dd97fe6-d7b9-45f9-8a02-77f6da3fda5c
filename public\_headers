# Security and performance headers for Cloudflare Pages

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://n8n-maximizeai-n8n.duckdns.org; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://n8n-maximizeai-n8n.duckdns.org https://api.youtube.com; frame-ancestors 'none';

# Cache static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
*.png
  Cache-Control: public, max-age=31536000
*.jpg
  Cache-Control: public, max-age=31536000
*.jpeg
  Cache-Control: public, max-age=31536000
*.gif
  Cache-Control: public, max-age=31536000
*.webp
  Cache-Control: public, max-age=31536000
*.svg
  Cache-Control: public, max-age=31536000

# Cache fonts
*.woff
  Cache-Control: public, max-age=31536000
*.woff2
  Cache-Control: public, max-age=31536000
*.ttf
  Cache-Control: public, max-age=31536000
*.eot
  Cache-Control: public, max-age=31536000

# Don't cache HTML
*.html
  Cache-Control: public, max-age=0, must-revalidate
