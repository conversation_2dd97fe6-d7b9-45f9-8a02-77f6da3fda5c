import React, { useMemo, useState } from 'react';

// Minimal subset of fields we need. Keep it flexible because the dashboard data
// is user-populated and may have missing values.
export interface VideoRow {
  youtubeUrl?: string;
  title?: string;
  channelName?: string;
  channel?: string;
  duration?: number | string; // seconds or string
  views?: number;
  likes?: number;
  comments?: number;
  engagementRate?: number; // already a percentage (0-1 or 0-100)
  viewsPerHour?: number;
  tags?: string[] | string;
  publishedDate?: string;
  [key: string]: any;
}

const metricDefs: { key: keyof VideoRow; label: string; formatter?: (v: any) => string }[] = [
  { key: 'views', label: 'Views', formatter: (v) => Number(v || 0).toLocaleString() },
  { key: 'likes', label: 'Likes', formatter: (v) => Number(v || 0).toLocaleString() },
  { key: 'comments', label: 'Comments', formatter: (v) => Number(v || 0).toLocaleString() },
  {
    key: 'engagementRate',
    label: 'Engagement Rate',
    formatter: (v) => {
      if (v === null || v === undefined) return '-';
      let n = Number(v);
      if (isNaN(n)) return '-';
      // Display as decimal (0.150 format)
      return n.toFixed(3);
    },
  },
  { key: 'viewsPerHour', label: 'Views / Hour', formatter: (v) => Number(v || 0).toLocaleString() },
  {
    key: 'duration',
    label: 'Duration',
    formatter: (v) => {
      const n = Number(v);
      if (isNaN(n)) return '-';
      const m = Math.floor(n / 60);
      const s = Math.round(n % 60);
      return `${m}m ${s}s`;
    },
  },
];

const parseTags = (t: VideoRow['tags']): string[] => {
  if (!t) return [];
  if (Array.isArray(t)) return t;
  try {
    const parsed = JSON.parse(t);
    if (Array.isArray(parsed)) return parsed;
  } catch (_) {}
  return String(t)
    .split(',')
    .map((tag) => tag.trim())
    .filter(Boolean);
};

const ABTestPanel: React.FC = () => {
  const [selectedA, setSelectedA] = useState<string>('');
  const [selectedB, setSelectedB] = useState<string>('');

  // Pull dashboard data once
  const videos: VideoRow[] = useMemo(() => {
    try {
      const raw = localStorage.getItem('dashboardData');
      if (!raw) return [];
      return JSON.parse(raw);
    } catch {
      return [];
    }
  }, []);

  const videoA = useMemo(() => videos.find((v) => v.youtubeUrl === selectedA), [videos, selectedA]);
  const videoB = useMemo(() => videos.find((v) => v.youtubeUrl === selectedB), [videos, selectedB]);

  const overlapTags = useMemo(() => {
    if (!videoA || !videoB) return [] as string[];
    const setA = new Set(parseTags(videoA.tags));
    const setB = new Set(parseTags(videoB.tags));
    return [...setA].filter((t) => setB.has(t));
  }, [videoA, videoB]);

  const uniqueATags = useMemo(() => {
    if (!videoA) return [] as string[];
    const A = parseTags(videoA.tags);
    if (!videoB) return A;
    const overlap = new Set(overlapTags);
    return A.filter((t) => !overlap.has(t));
  }, [videoA, videoB, overlapTags]);

  const uniqueBTags = useMemo(() => {
    if (!videoB) return [] as string[];
    const B = parseTags(videoB.tags);
    if (!videoA) return B;
    const overlap = new Set(overlapTags);
    return B.filter((t) => !overlap.has(t));
  }, [videoA, videoB, overlapTags]);

  // Simple insight: which video "wins" each metric (higher is better, except duration we consider closer?)
  const winnerForMetric = (key: keyof VideoRow): 'A' | 'B' | null => {
    if (!videoA || !videoB) return null;
    const aVal = Number(videoA[key] ?? 0);
    const bVal = Number(videoB[key] ?? 0);
    if (isNaN(aVal) || isNaN(bVal)) return null;
    if (key === 'duration') {
      // For duration, shorter may be better? We'll skip winner.
      return null;
    }
    return aVal === bVal ? null : aVal > bVal ? 'A' : 'B';
  };

  const predictiveInsight = useMemo(() => {
    if (!videoA || !videoB) return '';
    const erA = Number(videoA.engagementRate || 0);
    const erB = Number(videoB.engagementRate || 0);
    const vphA = Number(videoA.viewsPerHour || 0);
    const vphB = Number(videoB.viewsPerHour || 0);

    if (erA === erB && vphA === vphB) return 'Both videos perform similarly based on the selected metrics.';
    const best = erA * vphA > erB * vphB ? 'A' : 'B';
    return best === 'A'
      ? `${videoA.title ?? 'Video A'} currently shows stronger momentum and engagement; it is likely to outperform ${videoB.title ?? 'Video B'} over the next 24 hours.`
      : `${videoB.title ?? 'Video B'} currently shows stronger momentum and engagement; it is likely to outperform ${videoA.title ?? 'Video A'} over the next 24 hours.`;
  }, [videoA, videoB]);

  return (
    <div className="bg-gradient-to-br from-slate-900/50 to-gray-900/50 border border-yellow-500/30 rounded-xl p-6 shadow-2xl text-white backdrop-blur-sm">
      <h2 className="text-3xl font-bold text-yellow-300 mb-6 text-center flex items-center justify-center gap-3">
        <svg className="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
        </svg>
        Video Performance Comparison
      </h2>

      {/* Selection Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-900/20 to-indigo-900/20 p-4 rounded-lg border border-blue-400/30">
          <label className="block text-sm mb-2 font-semibold text-blue-200 flex items-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011 1v8a1 1 0 01-1 1H8a1 1 0 01-1-1V2a1 1 0 011-1z" />
            </svg>
            Video A
          </label>
          <select
            className="w-full bg-[#1a1a2e] border border-blue-400/30 rounded-lg px-3 py-3 text-white/90 focus:outline-none focus:border-blue-400 transition-all duration-300 hover:border-blue-400/50"
            value={selectedA}
            onChange={(e) => setSelectedA(e.target.value)}
          >
            <option value="">-- Select a video --</option>
            {videos.map((v) => (
              <option key={v.youtubeUrl} value={v.youtubeUrl}>
                {v.title?.slice(0, 80) || v.youtubeUrl}
              </option>
            ))}
          </select>
        </div>
        <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 p-4 rounded-lg border border-purple-400/30">
          <label className="block text-sm mb-2 font-semibold text-purple-200 flex items-center gap-2">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011 1v8a1 1 0 01-1 1H8a1 1 0 01-1-1V2a1 1 0 011-1z" />
            </svg>
            Video B
          </label>
          <select
            className="w-full bg-[#1a1a2e] border border-purple-400/30 rounded-lg px-3 py-3 text-white/90 focus:outline-none focus:border-purple-400 transition-all duration-300 hover:border-purple-400/50"
            value={selectedB}
            onChange={(e) => setSelectedB(e.target.value)}
          >
            <option value="">-- Select a video --</option>
            {videos.map((v) => (
              <option key={v.youtubeUrl} value={v.youtubeUrl}>
                {v.title?.slice(0, 80) || v.youtubeUrl}
              </option>
            ))}
          </select>
        </div>
      </div>

      {videoA && videoB ? (
        <>
          {/* Metrics Table */}
          <div className="overflow-x-auto bg-gradient-to-br from-gray-900/50 to-slate-900/50 rounded-xl border border-yellow-400/20 shadow-lg">
            <table className="min-w-full text-sm">
              <thead className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20">
                <tr>
                  <th className="py-4 px-4 text-left font-bold text-yellow-200">Metric</th>
                  <th className="py-4 px-4 text-left font-bold text-blue-300 w-48 bg-blue-900/20">Video A</th>
                  <th className="py-4 px-4 text-left font-bold text-purple-300 w-48 bg-purple-900/20">Video B</th>
                </tr>
              </thead>
              <tbody>
                {metricDefs.map(({ key, label, formatter }) => {
                  const win = winnerForMetric(key);
                  const aVal = formatter ? formatter(videoA[key]) : String(videoA[key] ?? '-');
                  const bVal = formatter ? formatter(videoB[key]) : String(videoB[key] ?? '-');
                  return (
                    <tr key={String(key)} className="border-t border-yellow-400/10 hover:bg-yellow-400/5 transition-all duration-300">
                      <td className="py-3 px-4 font-semibold text-yellow-200">{label}</td>
                      <td className={`py-3 px-4 font-bold transition-all duration-300 ${
                        win === 'A'
                          ? 'bg-green-600/30 text-green-200 border-l-4 border-green-400'
                          : 'text-blue-200 hover:bg-blue-900/10'
                      }`}>
                        {win === 'A' && <span className="mr-2">🏆</span>}
                        {aVal}
                      </td>
                      <td className={`py-3 px-4 font-bold transition-all duration-300 ${
                        win === 'B'
                          ? 'bg-green-600/30 text-green-200 border-l-4 border-green-400'
                          : 'text-purple-200 hover:bg-purple-900/10'
                      }`}>
                        {win === 'B' && <span className="mr-2">🏆</span>}
                        {bVal}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Tag overlap */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Unique Tags — Video A</h3>
              {uniqueATags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {uniqueATags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Overlapping Tags</h3>
              {overlapTags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {overlapTags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-green-600/40 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Unique Tags — Video B</h3>
              {uniqueBTags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {uniqueBTags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
          </div>

          {/* Predictive insight */}
          {predictiveInsight && (
            <div className="mt-10 bg-blue-900/40 border border-blue-400/60 rounded-xl p-4 text-center text-white/90 shadow">
              {predictiveInsight}
            </div>
          )}

          {/* Content Optimization Tools */}
          <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-br from-purple-900/30 to-indigo-900/30 rounded-xl p-6 border border-purple-400/20 shadow-lg">
              <h3 className="text-lg font-bold text-purple-300 mb-4 flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Title Performance Analysis
              </h3>
              <div className="space-y-3">
                <div className="text-sm text-white/80">
                  <span className="font-semibold text-purple-200">Video A Title Length:</span> {videoA.title?.length || 0} characters
                </div>
                <div className="text-sm text-white/80">
                  <span className="font-semibold text-purple-200">Video B Title Length:</span> {videoB.title?.length || 0} characters
                </div>
                <div className="text-sm text-purple-200 bg-purple-900/20 p-3 rounded-lg">
                  <strong>Insight:</strong> {
                    (videoA.title?.length || 0) > 60 || (videoB.title?.length || 0) > 60
                      ? "Consider shorter titles (under 60 chars) for better mobile visibility"
                      : "Title lengths are optimized for mobile viewing"
                  }
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-900/30 to-teal-900/30 rounded-xl p-6 border border-green-400/20 shadow-lg">
              <h3 className="text-lg font-bold text-green-300 mb-4 flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                </svg>
                Tag Effectiveness Score
              </h3>
              <div className="space-y-3">
                <div className="text-sm text-white/80">
                  <span className="font-semibold text-green-200">Video A Tags:</span> {parseTags(videoA.tags).length} tags
                </div>
                <div className="text-sm text-white/80">
                  <span className="font-semibold text-green-200">Video B Tags:</span> {parseTags(videoB.tags).length} tags
                </div>
                <div className="text-sm text-green-200 bg-green-900/20 p-3 rounded-lg">
                  <strong>Score:</strong> {
                    overlapTags.length > 0
                      ? `${overlapTags.length} shared tags suggest similar targeting strategy`
                      : "No shared tags - different audience targeting approaches"
                  }
                </div>
              </div>
            </div>
          </div>

          {/* Competitive Intelligence */}
          <div className="mt-8 bg-gradient-to-br from-orange-900/30 to-red-900/30 rounded-xl p-6 border border-orange-400/20 shadow-lg">
            <h3 className="text-lg font-bold text-orange-300 mb-4 flex items-center gap-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
            </svg>
              Channel Benchmarking
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-orange-900/20 p-4 rounded-lg">
                <div className="text-sm font-semibold text-orange-200 mb-2">Performance Comparison</div>
                <div className="text-xs text-white/80 space-y-1">
                  <div>Views/Hour Ratio: {((Number(videoA.viewsPerHour) || 0) / (Number(videoB.viewsPerHour) || 1)).toFixed(2)}x</div>
                  <div>Engagement Ratio: {((Number(videoA.engagementRate) || 0) / (Number(videoB.engagementRate) || 1)).toFixed(2)}x</div>
                </div>
              </div>
              <div className="bg-orange-900/20 p-4 rounded-lg">
                <div className="text-sm font-semibold text-orange-200 mb-2">Content Strategy</div>
                <div className="text-xs text-white/80">
                  {Number(videoA.duration) > Number(videoB.duration)
                    ? "Video A uses longer format strategy"
                    : Number(videoA.duration) < Number(videoB.duration)
                    ? "Video B uses longer format strategy"
                    : "Similar duration strategies"
                  }
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        <p className="text-center text-white/60">Select two videos to begin the comparison.</p>
      )}
    </div>
  );
};

export default ABTestPanel;
