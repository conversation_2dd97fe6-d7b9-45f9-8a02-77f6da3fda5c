import React from 'react';
import { Home, Bar<PERSON>hart2, Youtube, Instagram, Calendar, Settings, HelpCircle, Link, TrendingUp, FormInput, Play, Lightbulb } from 'lucide-react';
import { Tab } from '../App'; // Import Tab type from App.tsx

// Define the type for the props
interface SidebarProps {
  activeTab: Tab;
  setActiveTab: (tab: Tab) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, setActiveTab }) => {
  return (
    <div className="flex flex-col w-80 bg-[#121212] text-white h-screen p-6 shadow-lg fixed left-0 top-0 border-r border-white/5">
      {/* Logo */}
      <div className="flex items-center gap-3 mb-12 px-2 py-4">
        <div className="w-8 h-8 flex items-center justify-center bg-yellow-400 rounded-lg">
          <TrendingUp className="w-5 h-5 text-gray-900" />
        </div>
        <span className="text-xl font-bold text-yellow-300">YouTube Analytics</span>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1">
        <h2 className="text-xs font-semibold text-gray-400 uppercase mb-6 px-2">Main</h2>
        <ul className="space-y-3">
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('dashboard'); }} className={`flex items-center gap-3 px-4 py-3 rounded-lg ${activeTab === 'dashboard' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <Home className="w-5 h-5" />
              Dashboard
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('analysis'); }} className={`flex items-center gap-3 px-4 py-3 rounded-lg ${activeTab === 'analysis' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <FormInput className="w-5 h-5" />
              Research Form
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('saved'); }} className={`flex items-center gap-3 px-4 py-3 rounded-lg ${activeTab === 'saved' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <Link className="w-5 h-5" />
              Saved Links
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('analytics'); }} className={`flex items-center gap-3 px-4 py-3 rounded-lg ${activeTab === 'analytics' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <BarChart2 className="w-5 h-5" />
              Analytics
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('youtube'); }} className={`flex flex-col items-start px-4 py-3 rounded-lg ${activeTab === 'youtube' || activeTab === 'channel_performance' || activeTab === 'content_repurposing' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <Youtube className="w-5 h-5" />
                  YouTube
                </div>
                <span className="ml-auto text-xs text-black bg-yellow-400 px-2 py-0.5 rounded-full font-bold">2</span>
              </div>
              {(activeTab === 'youtube' || activeTab === 'channel_performance' || activeTab === 'content_repurposing') && (
                <ul className="ml-4 mt-3 space-y-2">
                  <li>
                    <a href="#" onClick={(e) => { e.preventDefault(); e.stopPropagation(); setActiveTab('channel_performance'); }} className={`flex items-center justify-between py-2 rounded-lg text-sm ${activeTab === 'channel_performance' ? 'bg-white/5 text-white/90' : 'text-white/70 hover:bg-white/5 transition-colors'}`}>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        <span>Channel Performance</span>
                      </div>
                      <span className="flex-shrink-0 ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
                    </a>
                  </li>
                  <li>
                    <a href="#" onClick={(e) => { e.preventDefault(); e.stopPropagation(); setActiveTab('content_repurposing'); }} className={`flex items-center justify-between py-2 rounded-lg text-sm ${activeTab === 'content_repurposing' ? 'bg-white/5 text-white/90' : 'text-white/70 hover:bg-white/5 transition-colors'}`}>
                      <div className="flex items-center gap-2">
                        <Lightbulb className="w-4 h-4" />
                        <span>Content Repurposing Ideas</span>
                      </div>
                      <span className="flex-shrink-0 ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
                    </a>
                  </li>
                </ul>
              )}
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('tiktok'); }} className={`flex items-center justify-between px-4 py-3 rounded-lg ${activeTab === 'tiktok' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <div className="flex items-center gap-3">
                <Play className="w-5 h-5" />
                TikTok
              </div>
              <span className="ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('instagram'); }} className={`flex items-center justify-between px-4 py-3 rounded-lg ${activeTab === 'instagram' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <div className="flex items-center gap-3">
                <Instagram className="w-5 h-5" />
                Instagram
              </div>
              <span className="ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
            </a>
          </li>
        </ul>

        {/* General Navigation */}
        <h2 className="text-xs font-semibold text-gray-400 uppercase mt-8 mb-6 px-2">General</h2>
        <ul className="space-y-3">
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('calendar'); }} className={`flex items-center justify-between gap-3 px-4 py-3 rounded-lg ${activeTab === 'calendar' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <Calendar className="w-5 h-5" />
              Calendar
              <span className="ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('settings'); }} className={`flex items-center justify-between gap-3 px-4 py-3 rounded-lg ${activeTab === 'settings' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <Settings className="w-5 h-5" />
              Settings
              <span className="ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
            </a>
          </li>
          <li>
            <a href="#" onClick={(e) => { e.preventDefault(); setActiveTab('help_support'); }} className={`flex items-center justify-between gap-3 px-4 py-3 rounded-lg ${activeTab === 'help_support' ? 'bg-white/10 text-white' : 'text-white/80 hover:bg-white/10 transition-colors'}`}>
              <HelpCircle className="w-5 h-5" />
              Help & Support
              <span className="ml-auto text-xs text-gray-500 bg-gray-700 px-2 py-0.5 rounded-full">Coming Soon!</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar; 