import React, { useEffect, useState } from 'react';
import { CheckCircle, ExternalLink, X } from 'lucide-react';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  spreadsheetUrl?: string;
  analysisTitle: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  spreadsheetUrl,
  analysisTitle,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      requestAnimationFrame(() => {
        setIsVisible(true);
      });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleClose = () => {
    setIsVisible(false);
    setIsExiting(true);
    setTimeout(onClose, 300);
  };

  const modalClasses = `
    fixed inset-0 z-50 flex items-center justify-center p-4
    transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}
    ${isExiting ? 'opacity-0' : ''}
  `;

  const overlayClasses = `
    absolute inset-0 bg-black/50 backdrop-blur-sm
    transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}
  `;

  const contentClasses = `
    relative bg-gradient-to-br from-gray-900/90 to-gray-800/90
    backdrop-blur-lg rounded-2xl p-8 max-w-md w-full
    shadow-2xl border border-white/20
    transform transition-all duration-300
    ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}
    ${isExiting ? 'scale-95 translate-y-4' : ''}
  `;

  return (
    <div className={modalClasses}>
      <div className={overlayClasses} onClick={handleClose} />
      
      <div className={contentClasses}>
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-white/10 transition-colors"
          aria-label="Close modal"
        >
          <X className="w-5 h-5 text-gray-400" />
        </button>

        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/20 mb-6">
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>

          <h3 className="text-2xl font-bold text-white mb-2">Analysis Started!</h3>
          
          <p className="text-gray-300 mb-4">
            Your analysis request for <span className="text-white font-medium">"{analysisTitle}"</span> has been submitted successfully.
          </p>

          {spreadsheetUrl && (
            <a
              href={spreadsheetUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="button-primary inline-flex items-center justify-center gap-2 mb-4"
            >
              <ExternalLink className="w-5 h-5" />
              View Results
            </a>
          )}

          <p className="text-sm text-gray-400">
            You will receive an email notification when the analysis is complete.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;