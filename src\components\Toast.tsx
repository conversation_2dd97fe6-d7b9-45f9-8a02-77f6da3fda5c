import React, { useEffect, useState } from 'react';
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react';
import { ToastNotification } from '../types';

interface ToastProps {
  notification: ToastNotification;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ notification, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Trigger enter animation
    requestAnimationFrame(() => {
      setIsVisible(true);
    });
    
    const timer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => onRemove(notification.id), 300);
    }, notification.duration || 5000);

    return () => {
      clearTimeout(timer);
    };
  }, [notification.id, notification.duration, onRemove]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => onRemove(notification.id), 300);
  };

  const iconProps = {
    className: 'w-5 h-5',
    'aria-hidden': true,
  };

  const getToastStyles = () => {
    const baseStyles = 'fixed flex items-center gap-3 p-4 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm border';
    const visibilityStyles = isVisible
      ? 'opacity-100 translate-y-0'
      : 'opacity-0 translate-y-2';
    const exitStyles = isExiting
      ? 'opacity-0 translate-x-full'
      : '';
    
    switch (notification.type) {
      case 'success':
        return `${baseStyles} ${visibilityStyles} ${exitStyles} bg-green-500/10 border-green-500/20 text-green-400`;
      case 'error':
        return `${baseStyles} ${visibilityStyles} ${exitStyles} bg-red-500/10 border-red-500/20 text-red-400`;
      case 'warning':
        return `${baseStyles} ${visibilityStyles} ${exitStyles} bg-yellow-500/10 border-yellow-500/20 text-yellow-400`;
      case 'info':
      default:
        return `${baseStyles} ${visibilityStyles} ${exitStyles} bg-blue-500/10 border-blue-500/20 text-blue-400`;
    }
  };

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle {...iconProps} />;
      case 'error':
        return <XCircle {...iconProps} />;
      case 'warning':
        return <AlertCircle {...iconProps} />;
      case 'info':
      default:
        return <Info {...iconProps} />;
    }
  };

  return (
    <div className={getToastStyles()}>
      {getIcon()}
      <p className="flex-1 text-sm font-medium text-white">
        {notification.message}
      </p>
      <button
        onClick={handleClose}
        className="p-1 rounded-full hover:bg-white/10 transition-colors"
        aria-label="Close notification"
      >
        <X className="w-4 h-4 text-white/60" />
      </button>
    </div>
  );
};

export default Toast;