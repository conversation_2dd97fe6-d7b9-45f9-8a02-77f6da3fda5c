import { useState, useCallback, useEffect } from 'react';
import { BarChart3, Link, Youtube } from 'lucide-react';
import AnalysisForm from './components/AnalysisForm';
import SavedLinks from './components/SavedLinks';
import SuccessModal from './components/SuccessModal';
import Toast from './components/Toast';
import { AnalysisFormData, ToastNotification } from './types';
import { saveLink } from './utils/storage';
import Analytics from './components/Analytics';
import LoadingSpinner from './components/LoadingSpinner';
import Sidebar from './components/Sidebar';
import DashboardPage from './components/DashboardPage';
import Login from './components/Login';
import Cookies from 'js-cookie';

// Update Tab type to reflect new sidebar navigation
export type Tab = 'analytics' | 'dashboard' | 'youtube' | 'tiktok' | 'instagram' | 'calendar' | 'settings' | 'help_support' | 'analysis' | 'saved' | 'channel_performance' | 'content_repurposing';

function App() {
  const [activeTab, setActiveTab] = useState<Tab>(() => {
    // Get initial tab from URL path
    const path = window.location.pathname.toLowerCase().replace('/', '');
    const validTabs: Tab[] = ['dashboard', 'analytics', 'analysis', 'saved', 'calendar', 'settings', 'youtube', 'tiktok', 'instagram'];
    return validTabs.includes(path as Tab) ? (path as Tab) : 'dashboard';
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [lastAnalysisTitle, setLastAnalysisTitle] = useState('');
  const [spreadsheetUrl, setSpreadsheetUrl] = useState('');
  const [toasts, setToasts] = useState<ToastNotification[]>([]);
  const [loggedIn, setLoggedIn] = useState(() => Cookies.get('ytresearch_logged_in') === 'true');

  const showToast = useCallback((type: ToastNotification['type'], message: string) => {
    const id = crypto.randomUUID();
    const newToast: ToastNotification = { id, type, message };
    setToasts(prev => [...prev, newToast]);
  }, []);

  // Handle tab changes and update URL
  const handleTabChange = useCallback((tab: Tab) => {
    setActiveTab(tab);
    // Update URL without page reload
    const newPath = `/${tab}`;
    window.history.pushState(null, '', newPath);
  }, []);

  // Handle browser back/forward buttons
  useEffect(() => {
    const handlePopState = () => {
      const path = window.location.pathname.toLowerCase().replace('/', '');
      const validTabs: Tab[] = ['dashboard', 'analytics', 'analysis', 'saved', 'calendar', 'settings', 'youtube', 'tiktok', 'instagram'];
      const newTab = validTabs.includes(path as Tab) ? (path as Tab) : 'dashboard';
      setActiveTab(newTab);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const handleFormSubmit = async (data: AnalysisFormData) => {
    setIsLoading(true);
    
    try {
      const webhookUrl = 'https://n8n-maximizeai-n8n.duckdns.org/webhook/197da747-a0d1-4775-898b-bd01d953af5a';
      
      const payload = {
        contentType: data.contentType,
        limited: data.searchLimit === 'limited',
        searchCount: data.searchLimit === 'limited' ? data.searchCount : null,
        blockedKeyword: data.blockedKeyword || null,
        email: data.email,
        timestamp: new Date().toISOString(),
        source: 'youtube-analytics-platform'
      };

      console.log('Sending payload to n8n:', payload);

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(payload),
        mode: 'cors'
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const responseData = await response.json();
        console.log('Response data:', responseData);
        // Save dashboard data to localStorage for dashboard use
        if (Array.isArray(responseData)) {
          localStorage.setItem('dashboardData', JSON.stringify(responseData));
        }
        // Extract spreadsheetUrl from array or object
        let spreadsheetUrl = undefined;
        if (Array.isArray(responseData) && responseData.length > 0 && responseData[0].spreadsheetUrl) {
          spreadsheetUrl = responseData[0].spreadsheetUrl;
        } else if (responseData.spreadsheetUrl) {
          spreadsheetUrl = responseData.spreadsheetUrl;
        }
        const analysisUrl = window.location.origin + '/#dashboard';
        saveLink(data.contentType, analysisUrl, spreadsheetUrl);
        setLastAnalysisTitle(data.contentType);
        setShowSuccessModal(true);
        showToast('success', 'Analysis request sent successfully! Dashboard updated.');
      } else {
        const errorText = await response.text();
        console.error('Webhook error response:', errorText);
        throw new Error(`Webhook returned ${response.status}: ${errorText || response.statusText}`);
      }
    } catch (error) {
      console.error('Error submitting to n8n webhook:', error);
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        showToast('error', 'Network error: Unable to connect to the analysis service. Please check your internet connection.');
      } else if (error instanceof Error && error.message.includes('CORS')) {
        showToast('error', 'CORS error: The analysis service is not properly configured for web requests.');
      } else {
        showToast('error', `Failed to start analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!loggedIn) {
    return <Login onLogin={() => setLoggedIn(true)} />;
  }

  return (
    <div className="min-h-screen bg-zinc-900 relative overflow-hidden flex">
      <Sidebar activeTab={activeTab} setActiveTab={handleTabChange} />
      <div className="flex-1 ml-80 relative z-10">
        {/* Header with glass effect */}
        <header className="bg-white/5 backdrop-blur-md border-b border-white/10 sticky top-0">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-2">
                {/* Removed YouTube logo and title */}
              </div>
              <div className="flex items-center gap-4">
                {/* Removed header navigation as sidebar will handle it */}
              </div>
            </div>
          </div>
        </header>

        {/* Main content with animation */}
        <main
          key={activeTab}
          className="px-4 sm:px-6 lg:px-8 py-8 animate-fade-in"
        >
          {activeTab === 'dashboard' && <DashboardPage />}
          {activeTab === 'analytics' && <Analytics />}
          {activeTab === 'analysis' && (
            <div className="card p-6 animate-slide-up">
              <AnalysisForm onSubmit={handleFormSubmit} isLoading={isLoading} />
            </div>
          )}
          {activeTab === 'saved' && (
            <div className="card p-6 animate-slide-up">
              <SavedLinks onShowToast={showToast} />
            </div>
          )}
          {(activeTab === 'youtube' || activeTab === 'channel_performance') && (
            <div className="flex flex-col items-center justify-center min-h-[40vh] bg-white/5 rounded-xl p-8 border border-white/10 shadow-lg text-center">
              <h3 className="text-2xl font-bold text-yellow-300 mb-4">Channel Performance</h3>
              <p className="text-white/80 text-lg mb-4">
                This tab will offer a comprehensive overview of your channel's overall health and growth, providing insights into audience demographics, subscriber growth trends, geographic performance, and content category effectiveness at a channel level.
              </p>
              <p className="text-white/70 italic mb-6">
                By consolidating key channel-wide metrics and trends, this will help you identify overarching strategies that drive sustained growth, understand your audience better, and make informed decisions about your content calendar and long-term channel direction. It complements individual video and comparison analyses by offering a broader perspective.
              </p>
              <div className="text-yellow-400 text-4xl font-extrabold animate-pulse">Coming Soon!</div>
            </div>
          )}
          {(activeTab === 'content_repurposing') && (
            <div className="flex flex-col items-center justify-center min-h-[40vh] bg-white/5 rounded-xl p-8 border border-white/10 shadow-lg text-center">
              <h3 className="text-2xl font-bold text-yellow-300 mb-4">Content Repurposing Ideas</h3>
              <p className="text-white/80 text-lg mb-4">
                This section will provide innovative ideas and strategies for repurposing your existing content across various platforms.
              </p>
              <p className="text-white/70 italic mb-6">
                Maximize your reach and impact by transforming your videos into blogs, social media snippets, podcasts, and more.
              </p>
              <div className="text-yellow-400 text-4xl font-extrabold animate-pulse">Coming Soon!</div>
            </div>
          )}
          {(activeTab === 'tiktok' || activeTab === 'instagram') && (
            <div className="flex flex-col items-center justify-center min-h-[40vh] bg-white/5 rounded-xl p-8 border border-white/10 shadow-lg text-center">
              <h3 className="text-2xl font-bold text-yellow-300 mb-4">Social Media Analytics</h3>
              <p className="text-white/80 text-lg mb-4">
                Dive deep into your TikTok and Instagram performance with detailed metrics and insights.
              </p>
              <p className="text-white/70 italic mb-6">
                Track follower growth, engagement rates, and content effectiveness across your social media channels.
              </p>
              <div className="text-yellow-400 text-4xl font-extrabold animate-pulse">Coming Soon!</div>
            </div>
          )}
          {(activeTab === 'calendar' || activeTab === 'settings' || activeTab === 'help_support') && (
            <div className="flex flex-col items-center justify-center min-h-[40vh] bg-white/5 rounded-xl p-8 border border-white/10 shadow-lg text-center">
              <h3 className="text-2xl font-bold text-yellow-300 mb-4">Feature Coming Soon!</h3>
              <p className="text-white/70 italic mb-6">
                Stay tuned for updates!
              </p>
              <div className="text-yellow-400 text-4xl font-extrabold animate-pulse">Coming Soon!</div>
            </div>
          )}
          
        </main>
      </div>

      {/* Processing Modal */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-gray-900/90 to-gray-800/90 rounded-2xl p-8 flex flex-col items-center shadow-2xl border border-white/20">
            <LoadingSpinner size="lg" className="mb-4" />
            <div className="text-white text-xl font-bold">Processing...</div>
            <div className="text-gray-300 mt-2">Please wait while we analyze your request.</div>
          </div>
        </div>
      )}

      {/* Modals and Toasts */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        spreadsheetUrl={spreadsheetUrl}
        analysisTitle={lastAnalysisTitle}
      />
      
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            notification={toast}
            onRemove={removeToast}
          />
        ))}
      </div>
    </div>
  );
}

export default App;