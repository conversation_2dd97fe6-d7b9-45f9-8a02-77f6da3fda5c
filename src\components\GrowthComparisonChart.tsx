import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ChartData {
  name: string;
  YouTube: number;
  TikTok: number;
  Instagram: number;
}

interface GrowthComparisonChartProps {
  activePlatform?: string;
}

const data: ChartData[] = [
  { name: 'Jan', YouTube: 6500, TikTok: 13000, Instagram: 16000 },
  { name: 'Feb', YouTube: 7000, TikTok: 14000, Instagram: 18000 },
  { name: 'Mar', YouTube: 8000, TikTok: 15500, Instagram: 20000 },
  { name: 'Apr', YouTube: 9000, TikTok: 17000, Instagram: 22000 },
  { name: 'May', YouTube: 10000, TikTok: 18500, Instagram: 24000 },
  { name: 'Jun', YouTube: 11000, TikTok: 20000, Instagram: 25000 },
];

const GrowthComparisonChart: React.FC<GrowthComparisonChartProps> = ({ activePlatform = 'YouTube' }) => {
  // Get platform colors
  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'YouTube': return '#ef4444';
      case 'TikTok': return '#f97316';
      case 'Instagram': return '#ec4899';
      default: return '#ef4444';
    }
  };

  // Transform data to show only the active platform with comparison to previous months
  const transformedData = data.map((item, index) => {
    const currentValue = item[activePlatform as keyof ChartData] as number;
    const previousValue = index > 0 ? data[index - 1][activePlatform as keyof ChartData] as number : currentValue;
    const growth = index > 0 ? ((currentValue - previousValue) / previousValue) * 100 : 0;

    return {
      name: item.name,
      followers: currentValue,
      growth: growth,
      previousMonth: previousValue
    };
  });
  return (
    <div>
      {/* Platform-specific header */}
      <div className="mb-4 text-center">
        <h3 className="text-lg font-semibold text-white mb-2">{activePlatform} Growth Trend</h3>
        <div className="flex justify-center items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: getPlatformColor(activePlatform) }}></div>
            <span className="text-white/70">Followers</span>
          </div>
          <div className="text-white/50">•</div>
          <div className="text-white/70">6-month trend</div>
        </div>
      </div>

      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          data={transformedData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#ffffff20" />
          <XAxis dataKey="name" stroke="#ffffff" />
          <YAxis stroke="#ffffff" tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`} />
          <Tooltip
            contentStyle={{
              backgroundColor: 'rgba(255,255,255,0.1)',
              border: '1px solid rgba(255,255,255,0.2)',
              borderRadius: '8px',
              backdropFilter: 'blur(10px)',
            }}
            labelStyle={{ color: '#fff' }}
            itemStyle={{ color: '#fff' }}
            formatter={(value: any, name: string) => {
              if (name === 'followers') {
                return [`${Number(value).toLocaleString()}`, `${activePlatform} Followers`];
              }
              if (name === 'growth') {
                return [`${Number(value).toFixed(1)}%`, 'Growth Rate'];
              }
              return [value, name];
            }}
          />
          <Line
            type="monotone"
            dataKey="followers"
            stroke={getPlatformColor(activePlatform)}
            strokeWidth={3}
            dot={{ r: 5, fill: getPlatformColor(activePlatform) }}
            activeDot={{ r: 7, fill: getPlatformColor(activePlatform) }}
          />
        </LineChart>
      </ResponsiveContainer>

      {/* Growth statistics */}
      <div className="mt-4 grid grid-cols-3 gap-4 text-center">
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-lg font-bold text-white">{transformedData[transformedData.length - 1]?.followers.toLocaleString()}</div>
          <div className="text-xs text-white/60">Current Followers</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-lg font-bold text-green-400">
            +{((transformedData[transformedData.length - 1]?.followers - transformedData[0]?.followers) || 0).toLocaleString()}
          </div>
          <div className="text-xs text-white/60">Total Growth</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3">
          <div className="text-lg font-bold text-blue-400">
            {transformedData[transformedData.length - 1]?.growth.toFixed(1)}%
          </div>
          <div className="text-xs text-white/60">Last Month</div>
        </div>
      </div>
    </div>
  );
};

export default GrowthComparisonChart; 