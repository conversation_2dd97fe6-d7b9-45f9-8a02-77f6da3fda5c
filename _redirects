# Cloudflare Pages redirects for SPA routing with specific routes
/dashboard    /index.html   200
/analytics    /index.html   200
/analysis     /index.html   200
/saved        /index.html   200
/calendar     /index.html   200
/settings     /index.html   200
/youtube      /index.html   200
/tiktok       /index.html   200
/instagram    /index.html   200

# Catch-all for any other routes (SPA fallback)
/*    /index.html   200

# Handle API routes if any
/api/*  /api/:splat  200

# Security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
