@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom radio button styling */
input[type="radio"] {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s;
  outline: none;
}

input[type="radio"]:checked {
  border-color: rgb(239, 68, 68);
  background-color: rgb(239, 68, 68);
}

/* Enhanced form animations */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.3); }
  50% { box-shadow: 0 0 30px rgba(255, 193, 7, 0.5), 0 0 40px rgba(255, 193, 7, 0.3); }
}

@keyframes float-gentle {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-3px) rotate(1deg); }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-float-gentle {
  animation: float-gentle 3s ease-in-out infinite;
}

/* Layered hover effect for form */
@keyframes subtle-lift {
  0% { transform: translateY(0px) scale(1); }
  100% { transform: translateY(-2px) scale(1.002); }
}

@keyframes content-float {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-1px); }
}

.form-container-hover {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-container-hover:hover .inner-content {
  animation: subtle-lift 0.5s ease-out forwards;
}

.form-container-hover:hover .content-section {
  animation: content-float 0.5s ease-out forwards;
}

/* Animated background */
.animated-bg {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
}

.animated-circle {
  position: absolute;
  border-radius: 9999px;
  mix-blend-mode: screen;
  filter: blur(64px);
  opacity: 0.2;
  animation: float 20s infinite ease-in-out;
}

.animated-circle:nth-child(1) {
  background-color: rgba(239, 68, 68, 0.3);
  width: 24rem;
  height: 24rem;
  top: -5rem;
  left: -5rem;
  animation-delay: 0s;
}

.animated-circle:nth-child(2) {
  background-color: rgba(249, 115, 22, 0.3);
  width: 30rem;
  height: 30rem;
  top: 50%;
  right: -5rem;
  animation-delay: -5s;
}

.animated-circle:nth-child(3) {
  background-color: rgba(234, 179, 8, 0.2);
  width: 40rem;
  height: 40rem;
  bottom: -10rem;
  left: 33.333333%;
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(5%, 10%) rotate(5deg) scale(1.1);
  }
  50% {
    transform: translate(-5%, -5%) rotate(-5deg) scale(0.9);
  }
  75% {
    transform: translate(10%, 5%) rotate(10deg) scale(1.05);
  }
}

.noise-overlay {
  position: fixed;
  inset: 0;
  pointer-events: none;
  opacity: 0.015;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.75' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

@layer components {
  .card {
    @apply bg-white/10 backdrop-blur-sm rounded-lg border border-white/10 
           shadow-xl transition-all duration-300 hover:shadow-2xl 
           hover:border-white/20 hover:scale-[1.02];
  }

  .input-field {
    @apply bg-white/5 border border-white/10 rounded-lg px-4 py-2
           focus:ring-2 focus:ring-white/20 focus:border-transparent
           transition-all duration-200 outline-none;
  }

  .button-primary {
    @apply bg-gradient-to-r from-red-600 to-orange-600
           hover:from-red-500 hover:to-orange-500
           text-white font-medium px-6 py-2 rounded-lg
           transform transition-all duration-200
           hover:scale-105 hover:shadow-lg
           active:scale-95 disabled:opacity-50;
  }

  .nav-tab {
    @apply px-4 py-2 rounded-lg transition-all duration-200
           hover:bg-white/10 font-medium
           flex items-center gap-2;
  }

  .nav-tab.active {
    @apply bg-white/15 text-white;
  }

  /* Animated background elements */
  .animated-bg {
    @apply fixed inset-0 overflow-hidden pointer-events-none;
  }

  .animated-circle {
    @apply absolute rounded-full mix-blend-screen filter blur-xl opacity-20;
    animation: float 20s infinite ease-in-out;
  }

  .animated-circle:nth-child(1) {
    @apply bg-red-500/30 w-96 h-96 -top-20 -left-20;
    animation-delay: 0s;
  }

  .animated-circle:nth-child(2) {
    @apply bg-orange-500/30 w-[30rem] h-[30rem] top-1/2 -right-20;
    animation-delay: -5s;
  }

  .animated-circle:nth-child(3) {
    @apply bg-yellow-500/20 w-[40rem] h-[40rem] -bottom-40 left-1/3;
    animation-delay: -10s;
  }

  @keyframes float {
    0%, 100% {
      transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
      transform: translate(5%, 10%) rotate(5deg) scale(1.1);
    }
    50% {
      transform: translate(-5%, -5%) rotate(-5deg) scale(0.9);
    }
    75% {
      transform: translate(10%, 5%) rotate(10deg) scale(1.05);
    }
  }

  .noise-overlay {
    @apply fixed inset-0 pointer-events-none opacity-[0.015];
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.75' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  }
}
