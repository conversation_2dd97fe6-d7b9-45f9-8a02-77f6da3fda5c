import { SavedLink } from '../types';

const STORAGE_KEY = 'youtube_analytics_links';
const LINK_EXPIRY_DAYS = 15;

export const saveLink = (title: string, url: string, spreadsheetUrl?: string): void => {
  const links = getSavedLinks();
  const newLink: SavedLink = {
    id: crypto.randomUUID(),
    title,
    url,
    createdAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + LINK_EXPIRY_DAYS * 24 * 60 * 60 * 1000).toISOString(),
    ...(spreadsheetUrl ? { spreadsheetUrl } : {}),
  };
  
  links.push(newLink);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(links));
};

export const getSavedLinks = (): SavedLink[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    
    const links: SavedLink[] = JSON.parse(stored);
    const now = new Date();
    
    // Filter out expired links
    const validLinks = links.filter(link => new Date(link.expiresAt) > now);
    
    // Update storage if we removed any expired links
    if (validLinks.length !== links.length) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(validLinks));
    }
    
    return validLinks;
  } catch {
    return [];
  }
};

export const removeLink = (id: string): void => {
  const links = getSavedLinks();
  const updatedLinks = links.filter(link => link.id !== id);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedLinks));
};

export const getExpiredLinksCount = (): number => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return 0;
    
    const links: SavedLink[] = JSON.parse(stored);
    const now = new Date();
    
    return links.filter(link => new Date(link.expiresAt) <= now).length;
  } catch {
    return 0;
  }
};

export const cleanupExpiredLinks = (): number => {
  const expiredCount = getExpiredLinksCount();
  getSavedLinks(); // This will automatically remove expired links
  return expiredCount;
};