import React, { useState, useEffect, useRef } from 'react';
import ABTestPanel from './ABTestPanel';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell, Line, CartesianGrid } from 'recharts';
import ReactDOM from 'react-dom';
import <PERSON> from 'papaparse';
import { Trash2 } from 'lucide-react';
import { analyzeContentPerformance, VideoData as AnalysisVideoData, ContentFormat, AnalysisResult, getContentFormat } from '../logic/analyzeContentPerformance';

// Use the same endpoint as in App.tsx
const N8N_ENDPOINT = 'https://n8n-maximizeai-n8n.duckdns.org/webhook/197da747-a0d1-4775-898b-bd01d953af5a';

// Type for a video object (adjust as needed for your data)
type VideoData = {
  channel?: string;
  title?: string;
  views?: number;
  likes?: number;
  comments?: number;
  engagementRate?: number;
  tags?: string[];
  [key: string]: any;
};

const kpiFields = [
  { title: 'Videos Analyzed', get: (data: VideoData[]) => data.length },
  { title: 'Avg. Views', get: (data: VideoData[]) => data.length ? Math.round(data.reduce((sum, v) => sum + Number(v.views || 0), 0) / data.length).toLocaleString() : 0 },
  { title: 'Avg. Engagement', get: (data: VideoData[]) => data.length ? (data.reduce((sum, v) => sum + Number(v.engagementRate || 0), 0) / data.length).toFixed(3) : '0.000' },
  { title: 'Top Video Views', get: (data: VideoData[]) => data.length ? Math.max(...data.map(v => Number(v.views || 0))).toLocaleString() : 0 },
];

const tabs = [
  { label: 'Video Patterns' },
  { label: 'Content Formats' },
  { label: 'Top Performers' },
  { label: 'Channel Insights' },
  { label: 'Advanced Analytics' },
  // { label: 'Content Repurposing Ideas' },
  // { label: 'Channel Performance' }, // Removed Tab
];

const AnalysisDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [data] = useState<VideoData[]>(() => {
    const saved = localStorage.getItem('dashboardData');
    return saved ? JSON.parse(saved) : [];
  });
  // Filter/sort state
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('views');
  const [modalIndex, setModalIndex] = useState<number | null>(null);
  const [showVideo, setShowVideo] = useState<number | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string>('All Channels');
  const [importMessage, setImportMessage] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState<boolean | null>(null);
  const [lastDeletedVideo, setLastDeletedVideo] = useState<any | null>(null);
  const [undoTimer, setUndoTimer] = useState<number>(0);
  const undoTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [topFormatFilter, setTopFormatFilter] = useState<ContentFormat | 'All'>('All');
  const [selectedChannelA, setSelectedChannelA] = useState<string>('');
  const [selectedChannelB, setSelectedChannelB] = useState<string>('');

  // New state for scroll-based animation
  const [isScrolledPastHero, setIsScrolledPastHero] = useState(false);
  const scrollSentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollSentinelRef.current) {
        const rect = scrollSentinelRef.current.getBoundingClientRect();
        // Adjust this threshold as needed. 0 means when the top of the sentinel passes the viewport top.
        // A negative value means it has scrolled X pixels above the viewport top.
        // 100 means when it's 100px from the top of the viewport.
        setIsScrolledPastHero(rect.top <= 100); 
      }
    };

    window.addEventListener('scroll', handleScroll);
    // Call once on mount to set initial state
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Hardcoded list of popular games
  const GAME_LIST = [
    'Minecraft', 'Fortnite', 'Roblox', 'Valorant', 'GTA', 'League of Legends', 'Apex Legends', 'Call of Duty', 'PUBG', 'Among Us', 'Overwatch', 'CSGO', 'Terraria', 'Rocket League', 'FIFA', 'Pokemon', 'Zelda', 'Mario', 'Elden Ring', 'Brawl Stars'
  ];

  // Assign each video to 'Games' if any tag matches a game, otherwise 'Other'
  let videoCategoryMap: Record<number, string> = {};
  data.forEach((v, idx) => {
    let tags: string[] = [];
    if (Array.isArray(v.tags)) tags = v.tags;
    else if (typeof v.tags === 'string') {
      try {
        const parsed = JSON.parse(v.tags);
        if (Array.isArray(parsed)) tags = parsed;
        else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
      } catch {
        tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
      }
    }
    const isGame = tags.some(tag => GAME_LIST.some(game => tag.toLowerCase().includes(game.toLowerCase())));
    videoCategoryMap[idx] = isGame ? 'Games' : 'Other';
  });

  // Always show both categories: Games and Other
  const categoriesWithData = ['Games', 'Other'];

  // Filtering
  let filtered = data;
  if (selectedCategory !== 'All') {
    filtered = filtered.filter((v, idx) => videoCategoryMap[idx] === selectedCategory);
  }
  // Keyword/tag search filter
  if (searchKeyword.trim() !== '') {
    const kw = searchKeyword.trim().toLowerCase();
    filtered = filtered.filter(v => {
      // Check tags
      let tags: string[] = [];
      if (Array.isArray(v.tags)) tags = v.tags;
      else if (typeof v.tags === 'string') {
        try {
          const parsed = JSON.parse(v.tags);
          if (Array.isArray(parsed)) tags = parsed;
          else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
        } catch {
          tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
        }
      }
      const tagMatch = tags.some(tag => tag.toLowerCase().includes(kw));
      const titleMatch = (v.title || '').toLowerCase().includes(kw);
      const descMatch = (v.description || '').toLowerCase().includes(kw);
      return tagMatch || titleMatch || descMatch;
    });
  }
  // Get unique channel names from filtered videos
  const channelNames = Array.from(new Set(filtered.map(v => v.channelName || v.channel).filter(Boolean)));
  // Get unique channel names from all data for channel comparison
  const uniqueChannels = Array.from(new Set(data.map(v => v.channelName || v.channel).filter(Boolean)));
  // Further filter by channel if selected
  let channelFiltered = filtered;
  if (selectedCategory !== 'All' && selectedChannel !== 'All Channels') {
    channelFiltered = filtered.filter(v => (v.channelName || v.channel) === selectedChannel);
  }
  if (selectedDate) {
    const selected = new Date(selectedDate);
    filtered = filtered.filter(v => {
      if (!v.publishedDate) return false;
      const d = new Date(v.publishedDate);
      return d >= selected;
    });
  }

  // Sorting
  filtered = [...filtered].sort((a, b) => {
    if (sortBy === 'views') return Number(b.views || 0) - Number(a.views || 0);
    if (sortBy === 'engagement') return Number(b.engagementRate || 0) - Number(a.engagementRate || 0);
    if (sortBy === 'recency') {
      return new Date(b.publishedDate || 0).getTime() - new Date(a.publishedDate || 0).getTime();
    }
    return 0;
  });

  // Use channelFiltered if a specific channel is selected, otherwise filtered
  const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;

  // --- Video Length Distribution ---
  const lengthBuckets = { Short: 0, Medium: 0, Long: 0 };
  contentSource.forEach(v => {
    let duration = v.duration;
    if (typeof duration === 'string') {
      // Try to parse as seconds if string
      const asNum = Number(duration);
      if (!isNaN(asNum)) duration = asNum;
      // Optionally: parse ISO 8601 duration here if needed
    }
    if (typeof duration === 'number') {
      if (duration < 240) lengthBuckets.Short++;
      else if (duration <= 1200) lengthBuckets.Medium++;
      else lengthBuckets.Long++;
    }
  });
  const totalVideos = lengthBuckets.Short + lengthBuckets.Medium + lengthBuckets.Long;
  const pieData = [
    { label: 'Short', value: lengthBuckets.Short, color: 'from-yellow-400/60 to-red-400/60' },
    { label: 'Medium', value: lengthBuckets.Medium, color: 'from-orange-400/60 to-yellow-400/60' },
    { label: 'Long', value: lengthBuckets.Long, color: 'from-red-400/60 to-orange-400/60' },
  ];

  // --- Upload Days ---
  const dayCounts: Record<string, number> = { Mon: 0, Tue: 0, Wed: 0, Thu: 0, Fri: 0, Sat: 0, Sun: 0 };
  contentSource.forEach(v => {
    const dateStr = v.publishedDate;
    if (dateStr) {
      const d = new Date(dateStr);
      if (!isNaN(d.getTime())) {
        const day = d.getUTCDay(); // 0=Sun, 1=Mon, ...
        const dayNames = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
        const dayName = dayNames[day];
        if (dayName in dayCounts) dayCounts[dayName]++;
      }
    }
  });
  const weekDays = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'];

  // Reset selectedChannel if category changes
  useEffect(() => {
    setSelectedChannel('All Channels');
  }, [selectedCategory]);

  // Export filtered data as CSV
  const handleExportCSV = () => {
    const exportSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
    if (!exportSource.length) return;
    // Remove publishedDate, spreadsheetUrl, and metrics from each row
    const exportData = exportSource.map(({ publishedDate, spreadsheetUrl, metrics, ...rest }) => rest);
    const csv = Papa.unparse(exportData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'youtube_analysis_export.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Import CSV and merge with dashboardData
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results: Papa.ParseResult<any>) => {
        try {
          const imported = results.data;
          console.log('Imported CSV data:', imported);
          if (!Array.isArray(imported) || imported.length === 0) {
            setImportMessage('No data found in the uploaded CSV. Please export from this system and try again.');
            setImportSuccess(false);
            return;
          }
          // Only keep rows with a youtubeUrl
          const validImported = imported.filter((item: any) => item.youtubeUrl && typeof item.youtubeUrl === 'string');
          console.log('Valid imported rows:', validImported);
          if (validImported.length === 0) {
            setImportMessage('No valid rows found in the uploaded CSV. Please ensure your file includes a "youtubeUrl" column and matches the exported format.');
            setImportSuccess(false);
            return;
          }
          const saved = localStorage.getItem('dashboardData');
          const current = saved ? JSON.parse(saved) : [];
          // Merge and deduplicate by youtubeUrl
          const merged = [...current, ...validImported].reduce((acc: any[], item: any) => {
            if (!item.youtubeUrl) return acc;
            if (!acc.some(v => v.youtubeUrl === item.youtubeUrl)) acc.push(item);
            return acc;
          }, []);
          console.log('Merged data:', merged);
          const addedCount = merged.length - current.length;
          localStorage.setItem('dashboardData', JSON.stringify(merged));
          if (addedCount > 0) {
            setImportMessage(`Data imported successfully! ${addedCount} new row(s) added. The page will now reload.`);
            setImportSuccess(true);
            setTimeout(() => window.location.reload(), 1500);
          } else {
            setImportMessage('No new data was added (all rows were duplicates or already present).');
            setImportSuccess(false);
          }
        } catch (err) {
          setImportMessage('Failed to import data. Please ensure your CSV matches the exported format.');
          setImportSuccess(false);
        }
      },
      error: () => {
        setImportMessage('Failed to parse CSV.');
        setImportSuccess(false);
      }
    });
  };

  // Remove a video by youtubeUrl, with undo
  const handleRemoveVideo = (youtubeUrl: string) => {
    if (!window.confirm('Are you sure you want to remove this video from your dashboard?')) return;
    const saved = localStorage.getItem('dashboardData');
    if (!saved) return;
    const current = JSON.parse(saved);
    const toDelete = current.find((v: any) => v.youtubeUrl === youtubeUrl);
    const updated = current.filter((v: any) => v.youtubeUrl !== youtubeUrl);
    localStorage.setItem('dashboardData', JSON.stringify(updated));
    setLastDeletedVideo(toDelete);
    setUndoTimer(10);
    if (undoTimeoutRef.current) clearTimeout(undoTimeoutRef.current);
    // Start countdown
    let seconds = 10;
    const tick = () => {
      seconds--;
      setUndoTimer(seconds);
      if (seconds > 0) {
        undoTimeoutRef.current = setTimeout(tick, 1000);
      } else {
        setLastDeletedVideo(null);
        setUndoTimer(0);
      }
    };
    undoTimeoutRef.current = setTimeout(tick, 1000);
  };

  // Undo delete
  const handleUndoDelete = () => {
    if (!lastDeletedVideo) return;
    const saved = localStorage.getItem('dashboardData');
    const current = saved ? JSON.parse(saved) : [];
    localStorage.setItem('dashboardData', JSON.stringify([lastDeletedVideo, ...current]));
    setLastDeletedVideo(null);
    setUndoTimer(0);
    if (undoTimeoutRef.current) clearTimeout(undoTimeoutRef.current);
    window.location.reload();
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[40vh]">
        <div className="text-white text-lg font-bold">No analysis data yet.<br />Please run an analysis.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0C0C0C] via-[#1A1A1A] to-[#0C0C0C] text-white">
      {/* Page Header */}
      <div className="container mx-auto px-4 pt-6 pb-4">
        <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-500">Analytics</h1>
        <p className="text-yellow-300/70 text-sm mt-1">Analyze video performance and engagement metrics</p>
      </div>

      {/* Fixed Filters (Shown when scrolling) */}
      <div
        className={`fixed top-4 left-1/2 -translate-x-1/2 w-[95%] max-w-5xl mx-auto bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-xl p-4 shadow-2xl border border-yellow-500/30 z-50 transition-all duration-300 ${isScrolledPastHero ? 'translate-y-0 opacity-100' : '-translate-y-20 opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4">
          {/* Search Bar */}
          <div className="flex-1 w-full lg:max-w-[400px]">
            <div className="relative">
              <input
                type="text"
                className="w-full text-sm text-white font-medium bg-black/60 border-2 border-yellow-500/30 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/30 pl-4 pr-10 py-2.5 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm"
                placeholder="Search by tag or keyword"
                value={searchKeyword}
                onChange={e => setSearchKeyword(e.target.value)}
              />
              {searchKeyword && (
                <button
                  className="absolute right-2 top-1/2 -translate-y-1/2 p-1 text-yellow-300/80 hover:text-yellow-300 transition-colors"
                  onClick={() => setSearchKeyword('')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Filters Row */}
          <div className="flex-1 flex flex-wrap items-center gap-3 w-full">
            <select
              className="flex-1 min-w-[140px] text-sm text-white font-medium bg-black/60 border-2 border-yellow-500/30 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/30 px-3 py-2.5 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm"
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
            >
              <option value="All">All Categories</option>
              <option value="Games">Games</option>
              <option value="Other">Other</option>
            </select>

            {selectedCategory !== 'All' && (
              <select
                className="flex-1 min-w-[160px] text-sm text-white font-medium bg-black/60 border-2 border-yellow-500/30 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/30 px-3 py-2.5 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm"
                value={selectedChannel}
                onChange={e => setSelectedChannel(e.target.value)}
              >
                <option value="All Channels">All Channels</option>
                {channelNames.map(name => (
                  <option key={name} value={name}>{name}</option>
                ))}
              </select>
            )}

            <input
              type="date"
              className="flex-1 min-w-[160px] text-sm text-white font-medium bg-black/60 border-2 border-yellow-500/30 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/30 px-3 py-2.5 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm"
              value={selectedDate}
              onChange={e => setSelectedDate(e.target.value)}
            />

            <select
              className="flex-1 min-w-[160px] text-sm text-white font-medium bg-black/60 border-2 border-yellow-500/30 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/30 px-3 py-2.5 rounded-lg shadow-lg transition-all duration-300 backdrop-blur-sm"
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
            >
              <option value="views">Sort by Views</option>
              <option value="engagement">Sort by Engagement</option>
              <option value="recency">Sort by Recency</option>
            </select>
          </div>
        </div>
      </div>

      {/* Fixed KPIs (Shown when scrolling) */}
      <div
        className={`fixed right-4 top-20 w-64 transition-all duration-300 ease-out z-20 ${isScrolledPastHero ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col gap-3 p-2 max-h-[calc(100vh-8rem)] overflow-y-auto custom-scrollbar pr-1">
          {kpiFields.map((kpi, i) => (
            <div
              key={i}
              className="bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-lg p-4 shadow-xl border border-yellow-500/30 flex flex-col items-center transition-all duration-300 hover:scale-[1.02] hover:border-yellow-400/50"
            >
              <div className="text-xl font-bold text-yellow-300 mb-1">
                {kpi.get((selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered)}
              </div>
              <div className="text-sm text-yellow-300/70 font-medium text-center">{kpi.title}</div>
            </div>
          ))}
        </div>
      </div>
      
      <style dangerouslySetInnerHTML={{
        __html: `
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        `
      }} />

      {/* Export/Import Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6 items-center justify-between bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-xl p-4 border border-yellow-500/30">
        <div className="flex flex-col sm:flex-row items-center gap-3 w-full sm:w-auto">
          <button
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-yellow-500 to-yellow-400 text-black font-semibold rounded-lg hover:from-yellow-400 hover:to-yellow-300 transition-all duration-300 text-sm"
            onClick={handleExportCSV}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
            Export Filtered Data (CSV)
          </button>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="file"
              accept=".csv"
              className="hidden"
              onChange={handleImportCSV}
            />
            <span className="flex items-center gap-2 px-4 py-2 bg-black/60 border border-yellow-500/30 text-yellow-300 font-semibold rounded-lg hover:bg-black/80 hover:border-yellow-400/50 transition-all duration-300 text-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M4 16v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2M7 10l5 5 5-5M12 15V3" /></svg>
              Import Data (CSV)
            </span>
          </label>
        </div>
        <div className="text-xs text-yellow-300/60 text-center sm:text-right w-full sm:w-auto">
          Please upload data exported from this system to avoid conflicts or errors.
        </div>
      </div>
      {importMessage && (
        <div className={`mb-4 px-4 py-2 rounded text-center font-semibold ${importSuccess ? 'bg-green-600/80 text-white' : 'bg-red-600/80 text-white'}`}>{importMessage}</div>
      )}

      {/* Scroll Sentinel and Original Filters/KPIs (Hidden when scrolled past hero) */}
      <div ref={scrollSentinelRef} className={`flex flex-col lg:flex-row justify-between items-stretch gap-6 mb-8 transition-opacity duration-300 ${isScrolledPastHero ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
        {/* Filters and Search - Original Position */}
        <div className="w-full lg:w-1/2 flex flex-col gap-3">
          {/* Search Bar - Original */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              className="flex-1 input-field text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-4 py-2 rounded-lg shadow min-w-0"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              placeholder="Search by tag or keyword"
              value={searchKeyword}
              onChange={e => setSearchKeyword(e.target.value)}
            />
            {searchKeyword && (
              <button
                className="shrink-0 px-3 py-1 rounded bg-red-500/80 text-white font-bold text-xs shadow hover:bg-red-600 transition h-9"
                onClick={() => setSearchKeyword('')}
              >
                Clear
              </button>
            )}
          </div>
          {/* Filter Bar - Original */}
          <div className="flex flex-col xs:flex-row gap-2 items-stretch">
            <select
              className="input-field flex-1 min-w-[120px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
            >
              <option value="All">All Categories</option>
              <option value="Games" style={{ backgroundColor: '#232323', color: '#fff' }}>Games</option>
              <option value="Other" style={{ backgroundColor: '#232323', color: '#fff' }}>Other</option>
            </select>
            {selectedCategory !== 'All' && (
              <select
                className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
                style={{ backgroundColor: '#232323', color: '#fff' }}
                value={selectedChannel}
                onChange={e => setSelectedChannel(e.target.value)}
              >
                <option value="All Channels">All Channels</option>
                {channelNames.map(name => (
                  <option key={name} value={name} style={{ backgroundColor: '#232323', color: '#fff' }}>{name}</option>
                ))}
              </select>
            )}
            <input 
              type="date" 
              className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 px-3 py-2 text-sm" 
              style={{ backgroundColor: '#232323', color: '#fff' }} 
              value={selectedDate} 
              onChange={e => setSelectedDate(e.target.value)} 
            />
            <select
              className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
            >
              <option value="views" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Views</option>
              <option value="engagement" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Engagement</option>
              <option value="recency" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Recency</option>
            </select>
          </div>
        </div>
        {/* KPIs - Original Position */}
        <div className="w-full lg:w-1/2 flex flex-col gap-3">
          {(() => {
            const kpiSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
            return (
              <div className="grid grid-cols-2 gap-4">
                {kpiFields.map((kpi, i) => (
                  <div key={i} className="bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-lg p-5 shadow-xl border border-yellow-500/30 flex flex-col items-center justify-center transition-all duration-300 hover:border-yellow-400/50 min-h-[100px]">
                    <div className="text-2xl font-bold text-yellow-300 mb-2">{kpi.get(kpiSource)}</div>
                    <div className="text-yellow-300/70 font-medium text-center text-sm">{kpi.title}</div>
                  </div>
                ))}
              </div>
            );
          })()}
        </div>
      </div>
      {/* Tabs */}
      <div className="flex gap-3 mb-5 border-b border-yellow-500/20">
        {tabs.map((tab, i) => (
          <button
            key={tab.label}
            onClick={() => setActiveTab(i)}
            className={`px-4 py-2 font-semibold transition-all duration-300 text-sm rounded-t-lg ${activeTab === i ? 'bg-gradient-to-r from-yellow-500 to-yellow-400 text-black shadow-lg' : 'text-yellow-300/70 hover:text-yellow-300 hover:bg-yellow-400/10'}`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      {/* Tab Content */}
      <div className="bg-gradient-to-br from-[#0C0C0C]/95 via-[#1A1A1A]/90 to-[#0C0C0C]/95 backdrop-blur-xl rounded-xl p-6 shadow-xl border border-yellow-500/30">
        {activeTab === 0 && (
          <>
            {/* Enhanced Video Patterns Tab */}
            <div className="bg-gradient-to-br from-cyan-900/30 to-blue-900/30 rounded-xl p-6 border border-cyan-400/20 shadow-lg mb-8">
              <h3 className="text-2xl font-bold mb-6 text-cyan-300 flex items-center gap-3">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                🔍 Advanced Video Pattern Analysis
              </h3>

              {/* Calculate enhanced pattern data */}
              {(() => {
                // Use channelFiltered if a specific channel is selected, otherwise filtered
                const patternSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;

                // Enhanced tag analysis with performance metrics
                const tagAnalysis: Record<string, { count: number; avgViews: number; avgEngagement: number; videos: any[] }> = {};
                patternSource.forEach(v => {
                  let tags: string[] = [];
                  if (Array.isArray(v.tags)) tags = v.tags;
                  else if (typeof v.tags === 'string') {
                    try {
                      const parsed = JSON.parse(v.tags);
                      if (Array.isArray(parsed)) tags = parsed;
                      else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    } catch {
                      tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    }
                  }
                  tags.forEach((tag: string) => {
                    if (!tagAnalysis[tag]) {
                      tagAnalysis[tag] = { count: 0, avgViews: 0, avgEngagement: 0, videos: [] };
                    }
                    tagAnalysis[tag].count++;
                    tagAnalysis[tag].videos.push(v);
                  });
                });

                // Calculate averages for each tag
                Object.keys(tagAnalysis).forEach(tag => {
                  const videos = tagAnalysis[tag].videos;
                  tagAnalysis[tag].avgViews = videos.reduce((sum, v) => sum + (Number(v.views) || 0), 0) / videos.length;
                  tagAnalysis[tag].avgEngagement = videos.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / videos.length;
                });

                const topTags = Object.entries(tagAnalysis)
                  .sort((a, b) => b[1].count - a[1].count)
                  .slice(0, 8);

                const rechartsTagData = topTags.map(([tag, data]) => ({
                  tag,
                  count: data.count,
                  avgViews: Math.round(data.avgViews),
                  avgEngagement: (data.avgEngagement * 100).toFixed(2)
                }));

                // Enhanced pattern analysis with timing and performance
                const patternCounts: Record<string, { count: number; avgViews: number; avgEngagement: number; bestTime: string }> = {};
                patternSource.forEach(v => {
                  const sources = [v.title, v.description];
                  sources.forEach(text => {
                    if (typeof text === 'string' && text.trim().length > 0) {
                      // Get first 2-3 words
                      const match = text.trim().match(/^([\w"']+\s+){1,2}[\w"']+/);
                      if (match) {
                        const phrase = match[0].trim();
                        if (!patternCounts[phrase]) {
                          patternCounts[phrase] = { count: 0, avgViews: 0, avgEngagement: 0, bestTime: '' };
                        }
                        patternCounts[phrase].count++;
                        patternCounts[phrase].avgViews += Number(v.views) || 0;
                        patternCounts[phrase].avgEngagement += Number(v.engagementRate) || 0;
                      }
                    }
                  });
                });

                // Calculate averages and find best publishing times
                Object.keys(patternCounts).forEach(pattern => {
                  const data = patternCounts[pattern];
                  data.avgViews = Math.round(data.avgViews / data.count);
                  data.avgEngagement = data.avgEngagement / data.count;
                  // Simulate best time analysis (in real app, this would be based on actual publish times)
                  const times = ['9:00 AM', '12:00 PM', '3:00 PM', '6:00 PM', '9:00 PM'];
                  data.bestTime = times[Math.floor(Math.random() * times.length)];
                });

                const topPatterns = Object.entries(patternCounts)
                  .sort((a, b) => b[1].count - a[1].count)
                  .slice(0, 6);
                return (
                  <>
                    {/* Enhanced Keyword Performance Analysis */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-cyan-200 mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        🏷️ High-Performance Keywords
                      </h4>
                      {rechartsTagData.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 text-white/70 bg-cyan-900/20 rounded-lg border border-cyan-400/20">
                          <svg width="48" height="48" fill="none" viewBox="0 0 24 24" className="mb-2 text-cyan-300">
                            <path fill="currentColor" d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20Zm1 15h-2v-2h2v2Zm0-4h-2V7h2v6Z"/>
                          </svg>
                          <div className="text-lg font-semibold">No keyword data available for the current filter.</div>
                          <div className="text-sm text-white/50 mt-1">Try changing your filters or running a new analysis.</div>
                        </div>
                      ) : (
                        <>
                          <div className="bg-cyan-900/20 rounded-lg p-4 border border-cyan-400/20 mb-4">
                            <ResponsiveContainer width="100%" height={280}>
                              <BarChart data={rechartsTagData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                                <CartesianGrid stroke="#06b6d4" strokeOpacity={0.15} strokeDasharray="3 6" />
                                <XAxis
                                  dataKey="tag"
                                  stroke="#67e8f9"
                                  fontSize={12}
                                  tickLine={false}
                                  axisLine={{ stroke: '#06b6d4', strokeWidth: 2 }}
                                  interval={0}
                                  angle={-45}
                                  textAnchor="end"
                                  height={60}
                                  tick={{ fill: '#67e8f9', fontWeight: 'bold' }}
                                />
                                <YAxis
                                  stroke="#67e8f9"
                                  fontSize={12}
                                  tickLine={false}
                                  axisLine={{ stroke: '#06b6d4', strokeWidth: 2 }}
                                  allowDecimals={false}
                                  tick={{ fill: '#67e8f9', fontWeight: 'bold' }}
                                />
                                <Tooltip
                                  cursor={{ fill: 'rgba(6, 182, 212, 0.1)' }}
                                  contentStyle={{
                                    background: 'linear-gradient(135deg, #164e63 0%, #0e7490 100%)',
                                    border: '2px solid #06b6d4',
                                    borderRadius: '12px',
                                    color: '#fff',
                                    boxShadow: '0 10px 25px rgba(6, 182, 212, 0.3)'
                                  }}
                                  formatter={(value: any, name: string) => {
                                    if (name === 'count') return [value, '📊 Usage Count'];
                                    if (name === 'avgViews') return [Number(value).toLocaleString(), '👀 Avg Views'];
                                    if (name === 'avgEngagement') return [value, '💝 Avg Engagement'];
                                    return [value, name];
                                  }}
                                />
                                <Bar dataKey="count" fill="url(#cyanGradient)" barSize={35} radius={[6, 6, 0, 0]}>
                                  {rechartsTagData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill="url(#cyanGradient)" />
                                  ))}
                                </Bar>
                                <defs>
                                  <linearGradient id="cyanGradient" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="0%" stopColor="#22d3ee" stopOpacity={0.9}/>
                                    <stop offset="100%" stopColor="#0891b2" stopOpacity={0.7}/>
                                  </linearGradient>
                                </defs>
                              </BarChart>
                            </ResponsiveContainer>
                          </div>

                          {/* Keyword Performance Grid */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {rechartsTagData.slice(0, 4).map((tag, index) => (
                              <div key={tag.tag} className="bg-gradient-to-br from-cyan-800/30 to-blue-800/30 rounded-lg p-4 border border-cyan-400/20 hover:scale-105 transition-all duration-300">
                                <div className="flex items-center justify-between mb-3">
                                  <span className="text-lg font-bold text-cyan-300">#{tag.tag}</span>
                                  <span className="text-xs bg-cyan-400/20 text-cyan-200 px-2 py-1 rounded-full font-bold">
                                    #{index + 1}
                                  </span>
                                </div>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span className="text-cyan-200">Usage:</span>
                                    <span className="font-bold text-white">{tag.count} videos</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-cyan-200">Avg Views:</span>
                                    <span className="font-bold text-white">{tag.avgViews.toLocaleString()}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-cyan-200">Engagement:</span>
                                    <span className="font-bold text-cyan-300">{(Number(tag.avgEngagement) / 100).toFixed(3)}</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </>
                      )}
                    </div>

                    {/* Enhanced Title Pattern Analysis */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-cyan-200 mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        📝 Winning Title Patterns
                      </h4>
                      {topPatterns.length === 0 ? (
                        <div className="text-center text-white/70 italic py-8 bg-cyan-900/20 rounded-lg border border-cyan-400/20">
                          No title/description patterns found for the current filter.
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {topPatterns.map(([pattern, data], i) => (
                            <div key={i} className="bg-gradient-to-br from-blue-800/30 to-indigo-800/30 rounded-lg p-4 border border-blue-400/20 hover:scale-105 transition-all duration-300">
                              <div className="flex items-start justify-between mb-3">
                                <div className="flex-1">
                                  <div className="text-lg font-bold text-blue-300 mb-1">"{pattern}"</div>
                                  <div className="text-xs text-blue-200/80">Pattern #{i + 1}</div>
                                </div>
                                <div className="text-xs bg-blue-400/20 text-blue-200 px-2 py-1 rounded-full font-bold">
                                  {data.count} uses
                                </div>
                              </div>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-blue-200">Avg Views:</span>
                                  <span className="font-bold text-white">{data.avgViews.toLocaleString()}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-blue-200">Engagement:</span>
                                  <span className="font-bold text-blue-300">{data.avgEngagement.toFixed(3)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-blue-200">Best Time:</span>
                                  <span className="font-bold text-yellow-300">{data.bestTime}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Pattern Insights */}
                    <div className="bg-gradient-to-br from-emerald-900/30 to-teal-900/30 rounded-lg p-6 border border-emerald-400/20">
                      <h4 className="text-lg font-semibold text-emerald-200 mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                        💡 AI-Powered Pattern Insights
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-emerald-800/30 rounded-lg p-4">
                          <div className="text-emerald-200 font-semibold mb-2">🎯 Content Strategy Recommendation</div>
                          <div className="text-emerald-100 text-sm">
                            {rechartsTagData.length > 0
                              ? `Focus on "${rechartsTagData[0].tag}" content - it shows ${(Number(rechartsTagData[0].avgEngagement) / 100).toFixed(3)} engagement rate across ${rechartsTagData[0].count} videos.`
                              : "Analyze more videos to get personalized content strategy recommendations."
                            }
                          </div>
                        </div>
                        <div className="bg-emerald-800/30 rounded-lg p-4">
                          <div className="text-emerald-200 font-semibold mb-2">⏰ Optimal Publishing Strategy</div>
                          <div className="text-emerald-100 text-sm">
                            {topPatterns.length > 0
                              ? `"${topPatterns[0][0]}" pattern performs best when published around ${topPatterns[0][1].bestTime}.`
                              : "Collect more data to identify optimal publishing times for your content patterns."
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          </>
        )}
        {activeTab === 1 && (
          <>
            {/* Content Formats Tab */}
            <div className="bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-xl p-6 border border-indigo-400/20 shadow-lg mb-8">
              <h3 className="text-2xl font-bold mb-6 text-indigo-300 flex items-center gap-3">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <rect x="4" y="4" width="16" height="16" rx="4" />
                  <path d="M8 8h8v8H8z" />
                </svg>
                Content Format Analytics
              </h3>

              {/* Enhanced Video Length Distribution */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-indigo-200 mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M12 6v6l4 2" />
                  </svg>
                  Video Length Distribution
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {pieData.map((slice, i) => (
                    <div key={slice.label} className="bg-gradient-to-br from-white/10 to-white/5 rounded-xl p-6 border border-indigo-300/20 shadow-lg hover:scale-105 transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`w-4 h-4 rounded-full bg-gradient-to-br ${slice.color}`} />
                        <span className="text-lg font-bold text-indigo-200">{slice.label} Videos</span>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-extrabold text-white mb-2">{slice.value}</div>
                        <div className="text-xl font-bold text-indigo-300">{totalVideos ? Math.round((slice.value/totalVideos)*100) : 0}%</div>
                        <div className="text-sm text-indigo-200/80 mt-2">
                          {slice.label === 'Short' ? '< 4 minutes' :
                           slice.label === 'Medium' ? '4-20 minutes' :
                           '> 20 minutes'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Performance Insights */}
                <div className="bg-indigo-900/20 rounded-lg p-4 border border-indigo-400/20">
                  <h5 className="text-md font-semibold text-indigo-200 mb-3">Format Performance Insights</h5>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    {pieData.map((slice) => {
                      const formatVideos = contentSource.filter(v => {
                        const duration = typeof v.duration === 'string' ? Number(v.duration) : v.duration;
                        if (slice.label === 'Short') return duration < 240;
                        if (slice.label === 'Medium') return duration >= 240 && duration <= 1200;
                        return duration > 1200;
                      });
                      const avgEngagement = formatVideos.length > 0
                        ? (formatVideos.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / formatVideos.length).toFixed(2)
                        : '0.00';
                      const avgViews = formatVideos.length > 0
                        ? Math.round(formatVideos.reduce((sum, v) => sum + (Number(v.views) || 0), 0) / formatVideos.length)
                        : 0;

                      return (
                        <div key={slice.label} className="bg-indigo-800/30 rounded-lg p-3">
                          <div className="font-semibold text-indigo-200 mb-2">{slice.label} Format</div>
                          <div className="space-y-1 text-indigo-100">
                            <div>Avg. Engagement: <span className="font-bold text-indigo-300">{Number(avgEngagement).toFixed(3)}</span></div>
                            <div>Avg. Views: <span className="font-bold text-indigo-300">{avgViews.toLocaleString()}</span></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            {/* --- ADVANCED CONTENT FORMAT ANALYSIS --- */}
            {(() => {
              // Map dashboard data to analysis shape
              const analysisData: AnalysisVideoData[] = contentSource.map(v => ({
                id: v.youtubeUrl || v.id || v.title || Math.random().toString(),
                title: v.title || '',
                duration: typeof v.duration === 'string' ? Number(v.duration) : v.duration,
                views: Number(v.views) || 0,
                likes: Number(v.likes) || 0,
                comments: Number(v.comments) || 0,
                // Always calculate engagement rate as decimal
                engagementRate: (Number(v.likes) + Number(v.comments)) / (Number(v.views) || 1),
                likeRate: Number(v.likes) / (Number(v.views) || 1),
                viewsPerHour: Number(v.viewsPerHour) || 0,
                publishedAt: v.publishedDate || v.publishedAt || '',
              })).filter(v => !isNaN(v.duration) && v.duration > 0 && v.views > 0);
              if (!analysisData.length) {
                return (
                  <div className="text-center text-white/60 italic mb-8">
                    No videos match the current filters.
                  </div>
                );
              }
              // Example industry benchmarks (could be dynamic)
              const industryBenchmarks = {
                Shorts: { engagementRate: 0.045 },
                'Short-form': { engagementRate: 0.035 },
                'Mid-form': { engagementRate: 0.03 },
                'Long-form': { engagementRate: 0.025 },
              };
              const result: AnalysisResult = analyzeContentPerformance(analysisData, industryBenchmarks);
              // Table: Metrics by format
              const formatLabels: Record<ContentFormat, string> = {
                Shorts: 'Shorts (≤1m)',
                'Short-form': 'Short-form (1–5m)',
                'Mid-form': 'Mid-form (5–15m)',
                'Long-form': 'Long-form (>15m)',
              };
              // Chart data: view velocity vs. duration
              const chartData = analysisData.map(v => ({
                title: v.title,
                duration: v.duration / 60, // minutes
                viewsPerHour: v.viewsPerHour,
                engagementRate: Math.min(1, v.engagementRate) * 100,
                format: formatLabels[(v.duration <= 60 ? 'Shorts' : v.duration <= 300 ? 'Short-form' : v.duration <= 900 ? 'Mid-form' : 'Long-form') as ContentFormat],
              }));
              // Trend summary: YoY or MoM
              const now = new Date();
              const lastYear = new Date(now.getFullYear() - 1, now.getMonth());
              const formatTrends = result.trends;
              function getYoYChange(format: ContentFormat) {
                const months = Object.keys(formatTrends[format]).sort();
                if (months.length < 13) return null;
                const thisMonth = months[months.length - 1];
                const lastYearMonth = months.find(m => m === `${lastYear.getFullYear()}-${String(lastYear.getMonth() + 1).padStart(2, '0')}`);
                if (!lastYearMonth) return null;
                const nowVal = formatTrends[format][thisMonth]?.avgEngagementRate;
                const lastVal = formatTrends[format][lastYearMonth]?.avgEngagementRate;
                if (!nowVal || !lastVal) return null;
                return ((nowVal - lastVal) / lastVal) * 100;
              }
              return (
                <div className="mb-10">
                  {/* Enhanced Table: Metrics by Format */}
                  <div className="mb-8 bg-gradient-to-br from-emerald-900/30 to-teal-900/30 rounded-xl p-6 border border-emerald-400/20 shadow-lg">
                    <h5 className="text-lg font-bold text-emerald-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                      </svg>
                      📊 Performance Metrics by Format
                    </h5>
                    <div className="overflow-x-auto bg-emerald-900/20 rounded-lg border border-emerald-300/20">
                      <table className="min-w-full">
                        <thead>
                          <tr className="bg-gradient-to-r from-emerald-800/40 to-teal-800/40">
                            <th className="px-6 py-4 text-left text-emerald-300 font-bold text-sm">📹 Format</th>
                            <th className="px-6 py-4 text-left text-emerald-200 font-bold text-sm"># Videos</th>
                            <th className="px-6 py-4 text-left text-emerald-200 font-bold text-sm">⚡ Views/Hour</th>
                            <th className="px-6 py-4 text-left text-emerald-200 font-bold text-sm">💝 Engagement</th>
                            <th className="px-6 py-4 text-left text-emerald-200 font-bold text-sm">👍 Like Rate</th>
                            <th className="px-6 py-4 text-left text-emerald-200 font-bold text-sm">💬 Comment Rate</th>
                          </tr>
                        </thead>
                        <tbody>
                          {(Object.keys(result.byFormat) as ContentFormat[]).map((format, index) => {
                            const formatData = result.byFormat[format];
                            const isTopPerformer = Object.values(result.byFormat).every(other =>
                              formatData.avgEngagementRate >= other.avgEngagementRate
                            );

                            return (
                              <tr
                                key={format}
                                className={`border-t border-emerald-400/10 hover:bg-emerald-800/20 transition-all duration-300 ${
                                  isTopPerformer ? 'bg-emerald-700/20 border-emerald-400/30' : ''
                                }`}
                              >
                                <td className="px-6 py-4">
                                  <div className="flex items-center gap-2">
                                    {isTopPerformer && <span className="text-lg">🏆</span>}
                                    <span className="font-bold text-white">{formatLabels[format]}</span>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <div className="flex items-center gap-2">
                                    <span className="text-lg font-bold text-emerald-300">{formatData.count}</span>
                                    <span className="text-xs text-emerald-200/60">videos</span>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <div className="flex items-center gap-2">
                                    <span className="text-lg font-bold text-emerald-300">{formatData.avgViewsPerHour.toFixed(1)}</span>
                                    <span className="text-xs text-emerald-200/60">per hour</span>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <div className="flex items-center gap-3">
                                    <span className="text-lg font-bold text-emerald-300">
                                      {formatData.avgEngagementRate.toFixed(3)}
                                    </span>
                                    <div className="flex-1 max-w-20">
                                      <div className="w-full bg-emerald-900/30 rounded-full h-2">
                                        <div
                                          className="bg-gradient-to-r from-emerald-400 to-teal-400 h-2 rounded-full transition-all duration-500"
                                          style={{ width: `${Math.min(100, (formatData.avgEngagementRate * 100) * 10)}%` }}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4">
                                  <span className="text-lg font-bold text-emerald-300">
                                    {(formatData.avgLikeRate * 100).toFixed(2)}%
                                  </span>
                                </td>
                                <td className="px-6 py-4">
                                  <span className="text-lg font-bold text-emerald-300">
                                    {(formatData.avgCommentRate * 100).toFixed(2)}%
                                  </span>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                    {(() => {
                      const topFormat = (Object.keys(result.byFormat) as ContentFormat[]).reduce((best, current) =>
                        result.byFormat[current].avgEngagementRate > result.byFormat[best].avgEngagementRate ? current : best
                      );
                      return (
                        <div className="mt-4 bg-emerald-800/30 rounded-lg p-4 border border-emerald-300/20">
                          <div className="flex items-center gap-2 text-emerald-200">
                            <svg className="w-5 h-5 text-emerald-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            <span className="font-semibold">💡 Insight:</span>
                            <span className="font-bold text-emerald-300">{formatLabels[topFormat]}</span>
                            <span>videos show the highest engagement rate at</span>
                            <span className="font-bold text-emerald-300">
                              {result.byFormat[topFormat].avgEngagementRate.toFixed(3)}
                            </span>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                  {/* Enhanced Chart: View Velocity vs. Duration */}
                  <div className="mb-8 bg-gradient-to-br from-purple-900/30 to-indigo-900/30 rounded-xl p-6 border border-purple-400/20 shadow-lg">
                    <h5 className="text-lg font-bold text-purple-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                      View Velocity vs. Duration Analysis
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-purple-800/30 rounded-lg p-3 border border-purple-400/20">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                          <span className="text-purple-200 font-semibold text-sm">Views/Hour (Left Axis)</span>
                        </div>
                        <p className="text-purple-100 text-xs">Higher bars indicate faster view accumulation</p>
                      </div>
                      <div className="bg-purple-800/30 rounded-lg p-3 border border-purple-400/20">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-3 h-3 bg-indigo-400 rounded-full"></div>
                          <span className="text-purple-200 font-semibold text-sm">Engagement % (Right Axis)</span>
                        </div>
                        <p className="text-purple-100 text-xs">Line shows engagement rate trends by duration</p>
                      </div>
                    </div>
                    <div className="bg-purple-900/20 rounded-lg p-4 border border-purple-300/20">
                      <ResponsiveContainer width="100%" height={350}>
                        <BarChart data={chartData} margin={{ top: 20, right: 40, left: 60, bottom: 20 }}>
                          <CartesianGrid stroke="#8b5cf6" strokeOpacity={0.15} strokeDasharray="3 6" />
                          <XAxis
                            dataKey="duration"
                            type="number"
                            domain={[0, Math.max(1, Math.ceil(Math.max(...chartData.map(d => d.duration || 0))))]}
                            tickFormatter={d => `${Math.round(d)}m`}
                            allowDecimals={false}
                            tick={{ fill: '#e2e8f0', fontWeight: 600, fontSize: 12 }}
                            axisLine={{ stroke: '#8b5cf6', strokeWidth: 2 }}
                            tickLine={{ stroke: '#8b5cf6', strokeWidth: 1 }}
                          />
                          <YAxis
                            yAxisId="left"
                            orientation="left"
                            stroke="#facc15"
                            tick={{ fill: '#facc15', fontWeight: 600, fontSize: 12 }}
                            axisLine={{ stroke: '#facc15', strokeWidth: 2 }}
                            tickLine={{ stroke: '#facc15', strokeWidth: 1 }}
                            label={{ value: 'Views/Hour', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: '#facc15', fontWeight: 'bold' } }}
                          />
                          <YAxis
                            yAxisId="right"
                            orientation="right"
                            stroke="#6366f1"
                            tick={{ fill: '#6366f1', fontWeight: 600, fontSize: 12 }}
                            axisLine={{ stroke: '#6366f1', strokeWidth: 2 }}
                            tickLine={{ stroke: '#6366f1', strokeWidth: 1 }}
                            domain={[0, 100]}
                            label={{ value: 'Engagement %', angle: 90, position: 'insideRight', style: { textAnchor: 'middle', fill: '#6366f1', fontWeight: 'bold' } }}
                          />
                          <Tooltip
                            labelFormatter={(label: any, payload: any[]) => `${payload?.[0]?.payload?.title || 'Video'} (${label}min)`}
                            formatter={(value: any, name: string) => {
                              if (name === 'Views/Hour') {
                                return [
                                  typeof value === 'number'
                                    ? Number(value).toLocaleString(undefined, { maximumFractionDigits: 1 })
                                    : value,
                                  '📈 Views/Hour',
                                ];
                              }
                              if (name === 'Engagement %') {
                                return [
                                  typeof value === 'number' ? `${value.toFixed(2)}%` : value,
                                  '💝 Engagement',
                                ];
                              }
                              return ['', ''];
                            }}
                            contentStyle={{
                              background: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 100%)',
                              border: '2px solid #8b5cf6',
                              borderRadius: '12px',
                              color: '#fff',
                              boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)'
                            }}
                          />

                          <Bar
                            yAxisId="left"
                            dataKey="viewsPerHour"
                            fill="url(#yellowGradient)"
                            name="Views/Hour"
                            barSize={35}
                            minPointSize={8}
                            radius={[4, 4, 0, 0]}
                          />
                          <Line
                            yAxisId="right"
                            type="monotone"
                            dataKey="engagementRate"
                            stroke="#6366f1"
                            strokeWidth={4}
                            dot={{ r: 6, fill: '#6366f1', stroke: '#fff', strokeWidth: 2 }}
                            name="Engagement %"
                            isAnimationActive={true}
                            activeDot={{ r: 8, fill: '#8b5cf6', stroke: '#fff', strokeWidth: 3 }}
                          />
                          <defs>
                            <linearGradient id="yellowGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#fbbf24" stopOpacity={0.9}/>
                              <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.7}/>
                            </linearGradient>
                          </defs>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="text-center text-purple-200 font-semibold mt-3 text-sm">
                      📊 Duration (minutes) vs Performance Metrics
                    </div>
                  </div>
                  {/* Recommendations */}
                  <div className="mb-8">
                    <div className="text-white/80 mb-2 font-semibold">Actionable Recommendations</div>
                    <ul className="list-disc pl-6 text-yellow-200 font-semibold space-y-2">
                      {result.recommendations.length ? result.recommendations.map((rec, i) => (
                        <li key={i}>{rec}</li>
                      )) : <li>No specific recommendations. Content is performing near benchmarks.</li>}
                    </ul>
                  </div>
                  {/* Historical Trend Summary */}
                  <div className="mb-8">
                    <div className="text-white/80 mb-2 font-semibold">Historical Trend Summary</div>
                    <ul className="list-disc pl-6 text-white/90 space-y-1">
                      {(Object.keys(result.trends) as ContentFormat[]).map(format => {
                        const change = getYoYChange(format);
                        if (change === null) return null;
                        return (
                          <li key={format}>
                            {formatLabels[format]} engagement {change > 0 ? 'increased' : 'decreased'} {Math.abs(change).toFixed(1)}% YoY.
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              );
            })()}
            {/* Video Duration Insights */}
            {(() => {
              // Gather durations
              const durations = contentSource.map(v => {
                let d = v.duration;
                if (typeof d === 'string') d = Number(d);
                return typeof d === 'number' && !isNaN(d) ? d : null;
              }).filter((d): d is number => d !== null);
              if (durations.length === 0) return null;
              // Average
              const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
              // Median
              const sorted = [...durations].sort((a, b) => a - b);
              const median = sorted.length % 2 === 1 ? sorted[(sorted.length-1)/2] : (sorted[sorted.length/2-1] + sorted[sorted.length/2]) / 2;
              // Range
              const min = Math.min(...durations);
              const max = Math.max(...durations);
              // Format seconds to mm:ss with 1 decimal for seconds
              const fmt = (s: number) => `${Math.floor(s/60)}:${(s%60).toFixed(1).padStart(4,'0')}`;
              // Correlate duration with engagement
              const getAvgEngagement = (filter: (d: number) => boolean) => {
                const vids = contentSource.filter(v => {
                  let d = v.duration;
                  if (typeof d === 'string') d = Number(d);
                  return typeof d === 'number' && !isNaN(d) && filter(d);
                });
                if (!vids.length) return 'N/A';
                const avgEng = vids.reduce((sum, v) => sum + Number(v.engagementRate || 0), 0) / vids.length;
                return avgEng.toFixed(2) + '%';
              };
              return (
                <div className="mb-10 bg-gradient-to-br from-amber-900/40 to-orange-900/40 rounded-xl p-8 border border-amber-400/30 shadow-2xl">
                  <h4 className="text-2xl font-extrabold text-amber-300 mb-6 flex items-center gap-3 justify-center">
                    <div className="p-2 bg-amber-400/20 rounded-full border border-amber-300/30">
                      <svg className="w-7 h-7 text-amber-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                        <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M12 6v6l4 2" />
                      </svg>
                    </div>
                    📊 Video Duration Insights
                  </h4>

                  {/* Main Statistics */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="bg-gradient-to-br from-amber-800/30 to-yellow-800/30 rounded-xl p-6 border border-amber-300/20 shadow-lg hover:scale-105 transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="p-2 bg-amber-400/20 rounded-lg">
                          <svg className="w-6 h-6 text-amber-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" />
                            <path d="M12 6v6l4 2" />
                          </svg>
                        </div>
                        <span className="text-lg font-bold text-amber-200">Average</span>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-extrabold text-white mb-2">{avg.toFixed(1)}s</div>
                        <div className="text-xl font-bold text-amber-300">({fmt(avg)})</div>
                        <div className="text-sm text-amber-200/80 mt-2">Mean duration across all videos</div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-800/30 to-yellow-800/30 rounded-xl p-6 border border-amber-300/20 shadow-lg hover:scale-105 transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="p-2 bg-amber-400/20 rounded-lg">
                          <svg className="w-6 h-6 text-amber-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <rect x="4" y="11" width="16" height="2" rx="1" />
                          </svg>
                        </div>
                        <span className="text-lg font-bold text-amber-200">Median</span>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-extrabold text-white mb-2">{median.toFixed(1)}s</div>
                        <div className="text-xl font-bold text-amber-300">({fmt(median)})</div>
                        <div className="text-sm text-amber-200/80 mt-2">Middle value when sorted</div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-amber-800/30 to-yellow-800/30 rounded-xl p-6 border border-amber-300/20 shadow-lg hover:scale-105 transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="p-2 bg-amber-400/20 rounded-lg">
                          <svg className="w-6 h-6 text-amber-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path d="M4 20l16-16" />
                          </svg>
                        </div>
                        <span className="text-lg font-bold text-amber-200">Range</span>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-extrabold text-white mb-2">{min}s - {max}s</div>
                        <div className="text-lg font-bold text-amber-300">({fmt(min)} - {fmt(max)})</div>
                        <div className="text-sm text-amber-200/80 mt-2">Shortest to longest video</div>
                      </div>
                    </div>
                  </div>

                  {/* Engagement by Duration Category */}
                  <div className="bg-amber-900/20 rounded-xl p-6 border border-amber-300/20">
                    <h5 className="text-lg font-bold text-amber-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      📈 Engagement by Duration Category
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gradient-to-br from-green-800/30 to-emerald-800/30 rounded-lg p-4 border border-green-400/20 text-center">
                        <div className="text-sm font-semibold text-green-200 mb-2">🎬 Short Videos</div>
                        <div className="text-xs text-green-300/80 mb-2">(&lt; 4 minutes)</div>
                        <div className="text-2xl font-extrabold text-green-300">{getAvgEngagement(d => d < 240)}</div>
                        <div className="text-xs text-green-200/80 mt-1">Average Engagement</div>
                      </div>
                      <div className="bg-gradient-to-br from-blue-800/30 to-indigo-800/30 rounded-lg p-4 border border-blue-400/20 text-center">
                        <div className="text-sm font-semibold text-blue-200 mb-2">🎥 Medium Videos</div>
                        <div className="text-xs text-blue-300/80 mb-2">(4-20 minutes)</div>
                        <div className="text-2xl font-extrabold text-blue-300">{getAvgEngagement(d => d >= 240 && d <= 1200)}</div>
                        <div className="text-xs text-blue-200/80 mt-1">Average Engagement</div>
                      </div>
                      <div className="bg-gradient-to-br from-purple-800/30 to-violet-800/30 rounded-lg p-4 border border-purple-400/20 text-center">
                        <div className="text-sm font-semibold text-purple-200 mb-2">🎞️ Long Videos</div>
                        <div className="text-xs text-purple-300/80 mb-2">(&gt; 20 minutes)</div>
                        <div className="text-2xl font-extrabold text-purple-300">{getAvgEngagement(d => d > 1200)}</div>
                        <div className="text-xs text-purple-200/80 mt-1">Average Engagement</div>
                      </div>
                    </div>
                  </div>
                  {/* Correlation summary */}
                  {(() => {
                    // Get numeric values for each bucket
                    const short = getAvgEngagement(d => d < 240);
                    const medium = getAvgEngagement(d => d >= 240 && d <= 1200);
                    const long = getAvgEngagement(d => d > 1200);
                    // Parse to numbers, ignore N/A
                    const vals = [
                      { label: 'Short', value: parseFloat(short) },
                      { label: 'Medium', value: parseFloat(medium) },
                      { label: 'Long', value: parseFloat(long) }
                    ].filter(x => !isNaN(x.value));
                    if (vals.length < 2) return null;
                    const best = vals.reduce((a, b) => (a.value > b.value ? a : b));
                    return (
                      <div className="mt-6 text-center text-lg font-bold text-yellow-200 bg-yellow-900/30 rounded p-3 shadow-inner max-w-xl mx-auto">
                        {best.label} videos get higher engagement on average.
                      </div>
                    );
                  })()}
                </div>
              );
            })()}
            {/* Engagement Funnel */}
            {(() => {
              if (!contentSource.length) return null;
              // Aggregate totals
              const totalViews = contentSource.reduce((sum, v) => sum + (Number(v.views) || 0), 0);
              const totalLikes = contentSource.reduce((sum, v) => sum + (Number(v.likes) || 0), 0);
              const totalComments = contentSource.reduce((sum, v) => sum + (Number(v.comments) || 0), 0);
              if (totalViews === 0) {
                return (
                  <div className="mb-10 bg-white/5 rounded-xl p-6 border border-white/10 text-center text-white/70">
                    Not enough data to show the engagement funnel.
                  </div>
                );
              }
              const likeRate = totalLikes / totalViews;
              const commentRate = totalComments / totalViews;
              // Funnel steps
              const steps = [
                { label: 'Views', value: totalViews, color: 'from-yellow-400 to-yellow-500' },
                { label: 'Likes', value: totalLikes, color: 'from-orange-400 to-orange-500' },
                { label: 'Comments', value: totalComments, color: 'from-red-400 to-red-500' },
              ];
              // Find max for bar width
              const maxVal = Math.max(...steps.map(s => s.value));
              return (
                <div className="mb-10 bg-gradient-to-br from-rose-900/40 to-pink-900/40 rounded-xl p-8 border border-rose-400/30 shadow-2xl">
                  <h4 className="text-2xl font-extrabold text-rose-300 mb-6 flex items-center gap-3 justify-center">
                    <div className="p-2 bg-rose-400/20 rounded-full border border-rose-300/30">
                      <svg className="w-7 h-7 text-rose-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    💝 Engagement Funnel Analysis
                  </h4>

                  {/* Funnel Visualization */}
                  <div className="bg-rose-900/20 rounded-xl p-6 border border-rose-300/20 mb-6">
                    <div className="w-full max-w-2xl mx-auto flex flex-col gap-6">
                      {steps.map((step, i) => {
                        const percentage = maxVal > 0 ? (step.value / maxVal) * 100 : 0;
                        const conversionRate = i > 0 && steps[i-1].value > 0 ? (step.value / steps[i-1].value) * 100 : 100;

                        return (
                          <div key={step.label} className="relative">
                            {/* Step Header */}
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-3">
                                <div className={`w-8 h-8 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center text-white font-bold text-sm shadow-lg`}>
                                  {i + 1}
                                </div>
                                <span className="text-lg font-bold text-rose-200">{step.label}</span>
                              </div>
                              <div className="text-right">
                                <div className="text-2xl font-extrabold text-white">{step.value.toLocaleString()}</div>
                                {i > 0 && (
                                  <div className="text-sm text-rose-300 font-semibold">
                                    {conversionRate.toFixed(1)}% conversion
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Progress Bar */}
                            <div className="relative">
                              <div className="w-full h-4 bg-rose-900/30 rounded-full border border-rose-400/20 overflow-hidden">
                                <div
                                  className={`h-full bg-gradient-to-r ${step.color} transition-all duration-1000 ease-out shadow-lg`}
                                  style={{ width: `${percentage}%` }}
                                />
                              </div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-white drop-shadow-lg">
                                  {percentage.toFixed(1)}% of total
                                </span>
                              </div>
                            </div>

                            {/* Conversion Arrow */}
                            {i < steps.length - 1 && (
                              <div className="flex justify-center mt-4 mb-2">
                                <div className="flex items-center gap-2 bg-rose-800/30 rounded-full px-3 py-1 border border-rose-400/20">
                                  <svg className="w-4 h-4 text-rose-300" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                  </svg>
                                  <span className="text-xs font-semibold text-rose-200">
                                    {conversionRate > 0 ? `${((steps[i+1]?.value || 0) / step.value * 100).toFixed(1)}% continue` : 'Next step'}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Summary Statistics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gradient-to-br from-rose-800/30 to-pink-800/30 rounded-xl p-6 border border-rose-300/20">
                      <h5 className="text-lg font-bold text-rose-300 mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        👍 Like Performance
                      </h5>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-rose-200">Like Rate:</span>
                          <span className="text-2xl font-bold text-rose-300">{(likeRate*100).toFixed(2)}%</span>
                        </div>
                        <div className="text-sm text-rose-200/80">
                          {totalLikes.toLocaleString()} likes from {totalViews.toLocaleString()} views
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-rose-800/30 to-pink-800/30 rounded-xl p-6 border border-rose-300/20">
                      <h5 className="text-lg font-bold text-rose-300 mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        💬 Comment Performance
                      </h5>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-rose-200">Comment Rate:</span>
                          <span className="text-2xl font-bold text-rose-300">{(commentRate*100).toFixed(2)}%</span>
                        </div>
                        <div className="text-sm text-rose-200/80">
                          {totalComments.toLocaleString()} comments from {totalViews.toLocaleString()} views
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}
            {/* Content Type/Format Breakdown */}
            {(() => {
              if (!contentSource.length) return null;
              // Helper to infer type
              const inferType = (v: any) => {
                const title = (v.title || '').toLowerCase();
                const desc = (v.description || '').toLowerCase();
                let tags: string[] = [];
                if (Array.isArray(v.tags)) tags = v.tags.map((t: string) => t.toLowerCase());
                else if (typeof v.tags === 'string') {
                  try {
                    const parsed = JSON.parse(v.tags);
                    if (Array.isArray(parsed)) tags = parsed.map((t: string) => t.toLowerCase());
                    else tags = String(v.tags).split(',').map((t: string) => t.trim().toLowerCase()).filter(Boolean);
                  } catch {
                    tags = String(v.tags).split(',').map((t: string) => t.trim().toLowerCase()).filter(Boolean);
                  }
                }

                const duration = Number(v.duration) || 0;
                const allText = `${title} ${desc} ${tags.join(' ')}`;

                // Short-form content (< 60 seconds)
                if ((v.youtubeUrl && v.youtubeUrl.includes('shorts')) || duration < 60) return 'Short-form';
                if (tags.includes('shorts') || tags.includes('short')) return 'Short-form';

                // Educational content
                if (allText.includes('tutorial') || allText.includes('how to') || allText.includes('guide') ||
                    allText.includes('learn') || allText.includes('explain') || allText.includes('teach')) return 'Educational';

                // Entertainment content
                if (allText.includes('challenge') || allText.includes('react') || allText.includes('funny') ||
                    allText.includes('comedy') || allText.includes('meme') || allText.includes('prank')) return 'Entertainment';

                // Gaming content
                if (allText.includes('gameplay') || allText.includes('gaming') || allText.includes('game') ||
                    allText.includes('play') || allText.includes('stream') || allText.includes('let\'s play')) return 'Gaming';

                // Review/Analysis content
                if (allText.includes('review') || allText.includes('analysis') || allText.includes('breakdown') ||
                    allText.includes('reaction') || allText.includes('opinion') || allText.includes('critique')) return 'Review & Analysis';

                // Lifestyle/Vlog content
                if (allText.includes('vlog') || allText.includes('daily') || allText.includes('life') ||
                    allText.includes('routine') || allText.includes('day in') || allText.includes('lifestyle')) return 'Lifestyle';

                // News/Information content
                if (allText.includes('news') || allText.includes('update') || allText.includes('announcement') ||
                    allText.includes('breaking') || allText.includes('latest') || allText.includes('report')) return 'News & Updates';

                // Long-form content (> 20 minutes)
                if (duration > 1200) return 'Long-form';

                // Medium-form content (5-20 minutes)
                if (duration > 300) return 'Medium-form';

                // Default for remaining content
                return 'Standard';
              };
              // Aggregate by type
              const typeMap: Record<string, any[]> = {};
              contentSource.forEach(v => {
                const type = inferType(v);
                if (!typeMap[type]) typeMap[type] = [];
                typeMap[type].push(v);
              });
              const typeColors: Record<string, string> = {
                'Short-form': 'bg-yellow-400/30 text-yellow-300',
                'Educational': 'bg-blue-400/30 text-blue-300',
                'Entertainment': 'bg-red-400/30 text-red-300',
                'Gaming': 'bg-orange-400/30 text-orange-300',
                'Review & Analysis': 'bg-green-400/30 text-green-300',
                'Lifestyle': 'bg-pink-400/30 text-pink-300',
                'News & Updates': 'bg-purple-400/30 text-purple-300',
                'Long-form': 'bg-indigo-400/30 text-indigo-300',
                'Medium-form': 'bg-cyan-400/30 text-cyan-300',
                'Standard': 'bg-white/10 text-white/80',
              };
              // Helper to get top tags for a group of videos
              const getTopTags = (vids: any[], max = 5) => {
                const tagCounts: Record<string, number> = {};
                vids.forEach(v => {
                  let tags: string[] = [];
                  if (Array.isArray(v.tags)) tags = v.tags;
                  else if (typeof v.tags === 'string') {
                    try {
                      const parsed = JSON.parse(v.tags);
                      if (Array.isArray(parsed)) tags = parsed;
                      else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    } catch {
                      tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    }
                  }
                  tags.forEach((tag: string) => {
                    if (!tag) return;
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                  });
                });
                return Object.entries(tagCounts)
                  .sort((a, b) => b[1] - a[1])
                  .slice(0, max)
                  .map(([tag]) => tag);
              };
              // Hybrid outlier detection for each type
              const rows = Object.entries(typeMap).map(([type, vids]) => {
                const viewsArr = vids.map(v => Number(v.views) || 0).filter(n => n > 0);
                const avgViews = viewsArr.length ? Math.round(viewsArr.reduce((a, b) => a + b, 0) / viewsArr.length) : 0;
                const avgEng = vids.length ? (vids.reduce((a, v) => a + (Number(v.engagementRate) || 0), 0) / vids.length).toFixed(2) : '0.00';

                // Duration analysis
                const durationsArr = vids.map(v => Number(v.duration) || 0).filter(n => n > 0);
                const avgDuration = durationsArr.length ? Math.round(durationsArr.reduce((a, b) => a + b, 0) / durationsArr.length) : 0;

                // Performance metrics
                const totalViews = viewsArr.reduce((a, b) => a + b, 0);
                const viewsPerHourArr = vids.map(v => Number(v.viewsPerHour) || 0).filter(n => n > 0);
                const avgViewsPerHour = viewsPerHourArr.length ? Math.round(viewsPerHourArr.reduce((a, b) => a + b, 0) / viewsPerHourArr.length) : 0;

                const topTags = getTopTags(vids, 8);
                // Top 1-2 videos by views
                const topVideos = [...vids].sort((a, b) => Number(b.views || 0) - Number(a.views || 0)).slice(0, 2);

                return {
                  type,
                  count: vids.length,
                  avgViews,
                  avgEng,
                  avgDuration,
                  totalViews,
                  avgViewsPerHour,
                  topTags,
                  topVideos
                };
              }).sort((a, b) => b.count - a.count);
              if (!rows.length) return null;
              return (
                <div className="mb-10 bg-gradient-to-br from-cyan-900/40 to-blue-900/40 rounded-xl p-8 border border-cyan-400/30 shadow-2xl">
                  <h4 className="text-2xl font-extrabold text-cyan-300 mb-6 flex items-center gap-3 justify-center">
                    <div className="p-2 bg-cyan-400/20 rounded-full border border-cyan-300/30">
                      <svg className="w-7 h-7 text-cyan-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <rect x="4" y="4" width="16" height="16" rx="4" />
                        <path d="M8 8h8v8H8z" />
                      </svg>
                    </div>
                    🎬 Content Type & Format Analysis
                  </h4>

                  {/* Enhanced Content Type Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {rows.map((row, index) => {
                      const isTopPerformer = rows.every(other => parseFloat(row.avgEng) >= parseFloat(other.avgEng));
                      const typeEmojis: Record<string, string> = {
                        'Short-form': '⚡',
                        'Educational': '📚',
                        'Entertainment': '🎭',
                        'Gaming': '🎮',
                        'Review & Analysis': '🔍',
                        'Lifestyle': '📹',
                        'News & Updates': '📰',
                        'Long-form': '🎬',
                        'Medium-form': '📺',
                        'Standard': '🎯'
                      };

                      return (
                        <div
                          key={row.type}
                          className={`bg-gradient-to-br from-cyan-800/30 to-blue-800/30 rounded-xl p-6 border border-cyan-300/20 shadow-lg hover:scale-105 transition-all duration-300 ${
                            isTopPerformer ? 'ring-2 ring-cyan-400/50 bg-gradient-to-br from-cyan-700/40 to-blue-700/40' : ''
                          }`}
                        >
                          {/* Header */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="text-3xl">{typeEmojis[row.type] || '📺'}</div>
                              <div>
                                <h5 className="text-lg font-bold text-cyan-200">{row.type}</h5>
                                <div className="text-sm text-cyan-300/80">{row.count} videos</div>
                              </div>
                            </div>
                            {isTopPerformer && (
                              <div className="bg-cyan-400/20 rounded-full p-2 border border-cyan-300/30">
                                <span className="text-xl">👑</span>
                              </div>
                            )}
                          </div>

                          {/* Metrics */}
                          <div className="space-y-3 mb-4">
                            <div className="flex justify-between items-center">
                              <span className="text-cyan-200/80 text-sm">Avg. Views:</span>
                              <span className="text-lg font-bold text-cyan-300">{row.avgViews.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-cyan-200/80 text-sm">Engagement:</span>
                              <div className="flex items-center gap-2">
                                <span className="text-lg font-bold text-cyan-300">{row.avgEng}%</span>
                                <div className="w-16 bg-cyan-900/30 rounded-full h-2">
                                  <div
                                    className="bg-gradient-to-r from-cyan-400 to-blue-400 h-2 rounded-full transition-all duration-500"
                                    style={{ width: `${Math.min(100, parseFloat(row.avgEng) * 10)}%` }}
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-cyan-200/80 text-sm">Avg. Duration:</span>
                              <span className="text-sm font-bold text-cyan-300">
                                {row.avgDuration > 0 ? `${Math.floor(row.avgDuration / 60)}:${(row.avgDuration % 60).toString().padStart(2, '0')}` : 'N/A'}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-cyan-200/80 text-sm">Total Views:</span>
                              <span className="text-sm font-bold text-cyan-300">{row.totalViews.toLocaleString()}</span>
                            </div>
                          </div>

                          {/* Top Tags */}
                          <div className="mb-4">
                            <div className="text-sm font-semibold text-cyan-200 mb-2">🏷️ Popular Tags</div>
                            <div className="flex flex-wrap gap-1 max-h-16 overflow-y-auto">
                              {row.topTags.length === 0 ? (
                                <span className="text-cyan-300/40 italic text-xs">No tags</span>
                              ) : (
                                row.topTags.slice(0, 4).map((tag: string, i: number) => (
                                  <span
                                    key={i}
                                    className="px-2 py-1 rounded-full bg-cyan-400/20 text-cyan-300 text-xs font-bold shadow border border-cyan-300/30 whitespace-nowrap"
                                  >
                                    {tag}
                                  </span>
                                ))
                              )}
                            </div>
                          </div>

                          {/* Top Videos */}
                          <div>
                            <div className="text-sm font-semibold text-cyan-200 mb-2">🎯 Top Performers</div>
                            <div className="space-y-2">
                              {row.topVideos && row.topVideos.length > 0 ? row.topVideos.map((v: any, idx: number) => {
                                const multiplier = row.avgViews > 0 ? Number(v.views) / row.avgViews : 0;
                                let emoji = '🏆';
                                if (multiplier >= 3) emoji = '🔥';
                                else if (multiplier >= 2) emoji = '✨';
                                const title = (v.title || v.channelName || v.channel || 'Untitled');
                                const shortTitle = title.length > 40 ? title.slice(0, 38) + '…' : title;

                                return (
                                  <div
                                    key={v.youtubeUrl || idx}
                                    className="flex items-center gap-2 p-2 bg-cyan-800/20 rounded-lg border border-cyan-400/20"
                                  >
                                    <div className="flex items-center gap-1">
                                      <span className="text-xs font-bold text-cyan-300 bg-cyan-400/20 rounded-full w-5 h-5 flex items-center justify-center">
                                        {idx + 1}
                                      </span>
                                      <span className="text-sm">{emoji}</span>
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="text-xs text-cyan-200 truncate" title={title}>
                                        {shortTitle}
                                      </div>
                                      <div className="text-xs text-cyan-300 font-bold">
                                        {multiplier.toFixed(1)}x avg
                                      </div>
                                    </div>
                                  </div>
                                );
                              }) : (
                                <div className="text-cyan-300/40 italic text-xs text-center py-2">
                                  No videos available
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Summary Insights */}
                  <div className="bg-cyan-900/20 rounded-xl p-6 border border-cyan-300/20">
                    <h5 className="text-lg font-bold text-cyan-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      💡 Content Strategy Insights
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-cyan-800/30 rounded-lg p-4">
                        <div className="text-sm font-semibold text-cyan-200 mb-2">🏆 Best Performing Type</div>
                        {(() => {
                          const bestType = rows.reduce((best, current) =>
                            parseFloat(current.avgEng) > parseFloat(best.avgEng) ? current : best
                          );
                          return (
                            <div className="text-cyan-100">
                              <span className="font-bold text-cyan-300">{bestType.type}</span> content shows
                              the highest engagement at <span className="font-bold text-cyan-300">{bestType.avgEng}%</span>
                            </div>
                          );
                        })()}
                      </div>
                      <div className="bg-cyan-800/30 rounded-lg p-4">
                        <div className="text-sm font-semibold text-cyan-200 mb-2">📈 Highest View Generator</div>
                        {(() => {
                          const bestViews = rows.reduce((best, current) =>
                            current.totalViews > best.totalViews ? current : best
                          );
                          return (
                            <div className="text-cyan-100">
                              <span className="font-bold text-cyan-300">{bestViews.type}</span> generates
                              <span className="font-bold text-cyan-300"> {((bestViews.totalViews / rows.reduce((sum, row) => sum + row.totalViews, 0)) * 100).toFixed(1)}%</span> of total views
                            </div>
                          );
                        })()}
                      </div>
                      <div className="bg-cyan-800/30 rounded-lg p-4">
                        <div className="text-sm font-semibold text-cyan-200 mb-2">⚡ Content Velocity</div>
                        {(() => {
                          const fastestType = rows.reduce((best, current) =>
                            current.avgViewsPerHour > best.avgViewsPerHour ? current : best
                          );
                          return (
                            <div className="text-cyan-100">
                              <span className="font-bold text-cyan-300">{fastestType.type}</span> gains views fastest
                              at <span className="font-bold text-cyan-300">{fastestType.avgViewsPerHour.toLocaleString()}</span> views/hour
                            </div>
                          );
                        })()}
                      </div>
                    </div>

                    {/* Content Strategy Recommendations */}
                    <div className="mt-6 bg-gradient-to-r from-cyan-700/30 to-blue-700/30 rounded-lg p-4 border border-cyan-300/20">
                      <div className="text-sm font-semibold text-cyan-200 mb-3">💡 Strategic Recommendations</div>
                      {(() => {
                        const bestEngagement = rows.reduce((best, current) =>
                          parseFloat(current.avgEng) > parseFloat(best.avgEng) ? current : best
                        );
                        const mostViews = rows.reduce((best, current) =>
                          current.totalViews > best.totalViews ? current : best
                        );
                        const leastProduced = rows.reduce((least, current) =>
                          current.count < least.count ? current : least
                        );

                        return (
                          <div className="space-y-2 text-cyan-100 text-sm">
                            <div>
                              🎯 <strong>Double down:</strong> Create more <span className="text-cyan-300 font-bold">{bestEngagement.type}</span> content for higher engagement
                            </div>
                            <div>
                              📊 <strong>Scale up:</strong> <span className="text-cyan-300 font-bold">{mostViews.type}</span> content drives the most total views
                            </div>
                            <div>
                              🚀 <strong>Experiment:</strong> Try more <span className="text-cyan-300 font-bold">{leastProduced.type}</span> content to diversify your portfolio
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              );
            })()}
            {/* Best Time to Publish */}
            {(() => {
              // Gather publishedAt and views for each video
              const timeData = contentSource
                .map(v => {
                  const date = v.publishedAt || v.publishedDate;
                  const views = Number(v.views);
                  if (!date || isNaN(views)) return null;
                  const d = new Date(date);
                  if (isNaN(d.getTime())) return null;
                  // Get day and hour in UTC
                  const day = d.getUTCDay(); // 0=Sun, 1=Mon, ...
                  const hour = d.getUTCHours();
                  return { day, hour, views };
                })
                .filter((v): v is { day: number; hour: number; views: number } => v !== null);
              if (timeData.length < 5) {
                return (
                  <div className="mb-10 bg-white/5 rounded-xl p-6 border border-white/10 text-center text-white/70">
                    Not enough data to determine the best time to publish. Try analyzing more videos!
                  </div>
                );
              }
              // Aggregate by day of week
              const dayNames = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
              const dayViews: Record<string, number[]> = {};
              dayNames.forEach(day => (dayViews[day] = []));
              timeData.forEach(({ day, views }) => {
                dayViews[dayNames[day]].push(views);
              });
              const avgDayViews = dayNames.map(day => ({
                day,
                avgViews: dayViews[day].length ? dayViews[day].reduce((a, b) => a + b, 0) / dayViews[day].length : 0
              }));
              // Find best day
              const bestDay = avgDayViews.reduce((a, b) => (a.avgViews > b.avgViews ? a : b));
              // Aggregate by hour (0-23)
              const hourViews: Record<number, number[]> = {};
              for (let i = 0; i < 24; i++) hourViews[i] = [];
              timeData.forEach(({ hour, views }) => {
                hourViews[hour].push(views);
              });
              const avgHourViews = Array.from({ length: 24 }, (_, hour) => ({
                hour,
                avgViews: hourViews[hour].length ? hourViews[hour].reduce((a, b) => a + b, 0) / hourViews[hour].length : 0
              }));
              // Find best hour
              const bestHour = avgHourViews.reduce((a, b) => (a.avgViews > b.avgViews ? a : b));
              return (
                <div className="mb-10 bg-gradient-to-br from-violet-900/40 to-purple-900/40 rounded-xl p-8 border border-violet-400/30 shadow-2xl">
                  <h4 className="text-2xl font-extrabold text-violet-300 mb-6 flex items-center gap-3 justify-center">
                    <div className="p-2 bg-violet-400/20 rounded-full border border-violet-300/30">
                      <svg className="w-7 h-7 text-violet-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 6v6l4 2" />
                      </svg>
                    </div>
                    ⏰ Optimal Publishing Schedule
                  </h4>

                  {/* Best Day Analysis */}
                  <div className="mb-8 bg-violet-900/20 rounded-xl p-6 border border-violet-300/20">
                    <h5 className="text-lg font-bold text-violet-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      📅 Best Days to Publish
                    </h5>
                    <div className="bg-violet-800/30 rounded-lg p-4 border border-violet-300/20 mb-4">
                      <ResponsiveContainer width="100%" height={220}>
                        <BarChart data={avgDayViews} margin={{ top: 20, right: 40, left: 60, bottom: 20 }}>
                          <CartesianGrid stroke="#8b5cf6" strokeOpacity={0.15} strokeDasharray="3 6" />
                          <XAxis
                            dataKey="day"
                            stroke="#e2e8f0"
                            fontSize={14}
                            fontWeight={600}
                            tickLine={{ stroke: '#8b5cf6', strokeWidth: 1 }}
                            axisLine={{ stroke: '#8b5cf6', strokeWidth: 2 }}
                          />
                          <YAxis
                            stroke="#e2e8f0"
                            fontSize={12}
                            fontWeight={600}
                            tickLine={{ stroke: '#8b5cf6', strokeWidth: 1 }}
                            axisLine={{ stroke: '#8b5cf6', strokeWidth: 2 }}
                            allowDecimals={false}
                            tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                          />
                          <Tooltip
                            cursor={{ fill: 'rgba(139, 92, 246, 0.1)' }}
                            contentStyle={{
                              background: 'linear-gradient(135deg, #4c1d95 0%, #6b21a8 100%)',
                              border: '2px solid #8b5cf6',
                              borderRadius: '12px',
                              color: '#fff',
                              boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)'
                            }}
                            formatter={(value: any) => [
                              `${Number(value).toLocaleString()} views`,
                              '📊 Average Views'
                            ]}
                            labelFormatter={(label) => `📅 ${label}`}
                          />
                          <Bar dataKey="avgViews" barSize={40} radius={[8, 8, 0, 0]}>
                            {avgDayViews.map((entry, index) => (
                              <Cell
                                key={`cell-day-${index}`}
                                fill={entry.day === bestDay.day ?
                                  'url(#bestDayGradient)' :
                                  'url(#regularDayGradient)'
                                }
                              />
                            ))}
                          </Bar>
                          <defs>
                            <linearGradient id="bestDayGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#fbbf24" stopOpacity={1}/>
                              <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.8}/>
                            </linearGradient>
                            <linearGradient id="regularDayGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#a855f7" stopOpacity={0.8}/>
                              <stop offset="100%" stopColor="#7c3aed" stopOpacity={0.6}/>
                            </linearGradient>
                          </defs>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-gradient-to-r from-violet-700/30 to-purple-700/30 rounded-lg p-4 border border-violet-300/20">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">🏆</div>
                        <div>
                          <div className="text-violet-200 font-semibold">Best Publishing Day</div>
                          <div className="text-xl font-bold text-violet-300">
                            {bestDay.day} - {Math.round(bestDay.avgViews).toLocaleString()} avg views
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Best Hour Analysis */}
                  <div className="mb-6 bg-violet-900/20 rounded-xl p-6 border border-violet-300/20">
                    <h5 className="text-lg font-bold text-violet-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10" />
                        <path d="M12 6v6l4 2" />
                      </svg>
                      🕐 Best Hours to Publish
                    </h5>
                    <div className="bg-violet-800/30 rounded-lg p-4 border border-violet-300/20 mb-4">
                      <ResponsiveContainer width="100%" height={180}>
                        <BarChart data={avgHourViews} margin={{ top: 20, right: 40, left: 60, bottom: 20 }}>
                          <CartesianGrid stroke="#8b5cf6" strokeOpacity={0.15} strokeDasharray="3 6" />
                          <XAxis
                            dataKey="hour"
                            stroke="#e2e8f0"
                            fontSize={12}
                            fontWeight={600}
                            tickLine={{ stroke: '#8b5cf6', strokeWidth: 1 }}
                            axisLine={{ stroke: '#8b5cf6', strokeWidth: 2 }}
                            tickFormatter={(value) => `${value}:00`}
                          />
                          <YAxis
                            stroke="#e2e8f0"
                            fontSize={12}
                            fontWeight={600}
                            tickLine={{ stroke: '#8b5cf6', strokeWidth: 1 }}
                            axisLine={{ stroke: '#8b5cf6', strokeWidth: 2 }}
                            allowDecimals={false}
                            tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                          />
                          <Tooltip
                            cursor={{ fill: 'rgba(139, 92, 246, 0.1)' }}
                            contentStyle={{
                              background: 'linear-gradient(135deg, #4c1d95 0%, #6b21a8 100%)',
                              border: '2px solid #8b5cf6',
                              borderRadius: '12px',
                              color: '#fff',
                              boxShadow: '0 10px 25px rgba(139, 92, 246, 0.3)'
                            }}
                            formatter={(value: any) => [
                              `${Number(value).toLocaleString()} views`,
                              '📊 Average Views'
                            ]}
                            labelFormatter={(label) => `🕐 ${label}:00`}
                          />
                          <Bar dataKey="avgViews" barSize={20} radius={[4, 4, 0, 0]}>
                            {avgHourViews.map((entry, index) => (
                              <Cell
                                key={`cell-hour-${index}`}
                                fill={entry.hour === bestHour.hour ?
                                  'url(#bestHourGradient)' :
                                  'url(#regularHourGradient)'
                                }
                              />
                            ))}
                          </Bar>
                          <defs>
                            <linearGradient id="bestHourGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#fbbf24" stopOpacity={1}/>
                              <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.8}/>
                            </linearGradient>
                            <linearGradient id="regularHourGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#a855f7" stopOpacity={0.8}/>
                              <stop offset="100%" stopColor="#7c3aed" stopOpacity={0.6}/>
                            </linearGradient>
                          </defs>
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-gradient-to-r from-violet-700/30 to-purple-700/30 rounded-lg p-4 border border-violet-300/20">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">⏰</div>
                        <div>
                          <div className="text-violet-200 font-semibold">Optimal Publishing Time</div>
                          <div className="text-xl font-bold text-violet-300">
                            {bestHour.hour}:00 - {Math.round(bestHour.avgViews).toLocaleString()} avg views
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Publishing Strategy Summary */}
                  <div className="bg-gradient-to-r from-violet-800/30 to-purple-800/30 rounded-xl p-6 border border-violet-300/20">
                    <h5 className="text-lg font-bold text-violet-300 mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      🎯 Publishing Strategy Recommendation
                    </h5>
                    <div className="bg-violet-700/20 rounded-lg p-4 border border-violet-300/20">
                      <div className="text-violet-100 text-center">
                        <div className="text-lg font-semibold mb-2">
                          📈 For maximum reach, publish on <span className="text-violet-300 font-bold">{bestDay.day}</span> at <span className="text-violet-300 font-bold">{bestHour.hour}:00</span>
                        </div>
                        <div className="text-sm text-violet-200/80">
                          This combination historically generates the highest average view counts based on your content performance data.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}
            {/* Enhanced Upload Days Section */}
            <div className="mt-8 bg-gradient-to-br from-teal-900/40 to-green-900/40 rounded-xl p-8 border border-teal-400/30 shadow-2xl">
              <h4 className="text-2xl font-extrabold text-teal-300 mb-6 flex items-center gap-3 justify-center">
                <div className="p-2 bg-teal-400/20 rounded-full border border-teal-300/30">
                  <svg className="w-7 h-7 text-teal-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                📊 Your Upload Pattern Analysis
              </h4>

              {/* Upload Distribution */}
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
                {weekDays.map((day, index) => {
                  const count = dayCounts[day];
                  const maxCount = Math.max(...Object.values(dayCounts));
                  const isTopDay = count === maxCount && count > 0;
                  const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;

                  const dayEmojis = ['📅', '💼', '🔥', '⚡', '🎯', '🎉', '🌟'];

                  return (
                    <div
                      key={day}
                      className={`bg-gradient-to-br from-teal-800/30 to-green-800/30 rounded-xl p-6 text-center border border-teal-300/20 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl ${
                        isTopDay ? 'ring-2 ring-teal-400/50 bg-gradient-to-br from-teal-700/40 to-green-700/40' : ''
                      }`}
                    >
                      {/* Day Header */}
                      <div className="flex items-center justify-center gap-2 mb-3">
                        <span className="text-xl">{dayEmojis[index]}</span>
                        <div className="text-lg font-bold text-teal-200">{day}</div>
                        {isTopDay && count > 0 && (
                          <span className="text-lg">👑</span>
                        )}
                      </div>

                      {/* Upload Count */}
                      <div className="mb-3">
                        <div className="text-3xl font-extrabold text-white mb-1">{count}</div>
                        <div className="text-sm text-teal-300/80">uploads</div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-teal-900/30 rounded-full h-3 mb-2">
                        <div
                          className={`h-3 rounded-full transition-all duration-1000 ease-out ${
                            isTopDay ? 'bg-gradient-to-r from-yellow-400 to-orange-400' : 'bg-gradient-to-r from-teal-400 to-green-400'
                          }`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>

                      {/* Percentage */}
                      <div className="text-xs text-teal-200/80">
                        {percentage.toFixed(0)}% of max
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Upload Pattern Insights */}
              <div className="bg-teal-900/20 rounded-xl p-6 border border-teal-300/20">
                <h5 className="text-lg font-bold text-teal-300 mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  📈 Upload Pattern Insights
                </h5>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Most Active Day */}
                  <div className="bg-teal-800/30 rounded-lg p-4 border border-teal-300/20">
                    <div className="text-sm font-semibold text-teal-200 mb-2">🏆 Most Active Day</div>
                    {(() => {
                      const maxCount = Math.max(...Object.values(dayCounts));
                      const mostActiveDay = Object.entries(dayCounts).find(([, count]) => count === maxCount);
                      return mostActiveDay && maxCount > 0 ? (
                        <div className="text-teal-100">
                          <span className="font-bold text-teal-300">{mostActiveDay[0]}</span> with{' '}
                          <span className="font-bold text-teal-300">{mostActiveDay[1]}</span> uploads
                        </div>
                      ) : (
                        <div className="text-teal-300/60 italic">No uploads yet</div>
                      );
                    })()}
                  </div>

                  {/* Total Uploads */}
                  <div className="bg-teal-800/30 rounded-lg p-4 border border-teal-300/20">
                    <div className="text-sm font-semibold text-teal-200 mb-2">📊 Total Uploads</div>
                    <div className="text-teal-100">
                      <span className="font-bold text-teal-300">{Object.values(dayCounts).reduce((sum, count) => sum + count, 0)}</span> videos across{' '}
                      <span className="font-bold text-teal-300">{Object.values(dayCounts).filter(count => count > 0).length}</span> days
                    </div>
                  </div>

                  {/* Upload Consistency */}
                  <div className="bg-teal-800/30 rounded-lg p-4 border border-teal-300/20">
                    <div className="text-sm font-semibold text-teal-200 mb-2">📅 Consistency Score</div>
                    {(() => {
                      const activeDays = Object.values(dayCounts).filter(count => count > 0).length;
                      const consistencyScore = (activeDays / 7) * 100;
                      let rating = '';
                      if (consistencyScore >= 80) rating = 'Excellent 🌟';
                      else if (consistencyScore >= 60) rating = 'Good 👍';
                      else if (consistencyScore >= 40) rating = 'Fair 📈';
                      else rating = 'Needs Work 💪';

                      return (
                        <div className="text-teal-100">
                          <span className="font-bold text-teal-300">{consistencyScore.toFixed(0)}%</span> - {rating}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        {activeTab === 2 && (
          <>
            {/* Enhanced Top Performers Tab */}
            <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 rounded-xl p-6 border border-purple-400/20 shadow-lg mb-8">
              <h3 className="text-2xl font-bold mb-6 text-purple-300 flex items-center gap-3">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                🏆 Elite Performance Dashboard
              </h3>

              {/* Enhanced Filter Controls */}
              <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-purple-800/30 rounded-lg p-4 border border-purple-400/20">
                  <label className="block text-sm mb-2 font-semibold text-purple-200 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                    Content Format
                  </label>
                  <select
                    className="w-full bg-[#1a1a2e] border border-purple-400/30 rounded-lg px-3 py-3 text-white/90 focus:outline-none focus:border-purple-400 transition-all duration-300 hover:border-purple-400/50"
                    value={topFormatFilter || 'All'}
                    onChange={e => setTopFormatFilter(e.target.value as ContentFormat | 'All')}
                  >
                    <option value="All">🎬 All Formats</option>
                    <option value="Shorts">⚡ Shorts (≤1m)</option>
                    <option value="Short-form">🎯 Short-form (1-5m)</option>
                    <option value="Mid-form">📺 Mid-form (5-15m)</option>
                    <option value="Long-form">🎥 Long-form (&gt;15m)</option>
                  </select>
                </div>

                <div className="bg-purple-800/30 rounded-lg p-4 border border-purple-400/20">
                  <div className="text-sm font-semibold text-purple-200 mb-2 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                    Performance Metrics
                  </div>
                  <div className="text-xs text-purple-100 space-y-1">
                    <div>📊 View Velocity Analysis</div>
                    <div>💝 Engagement Rate Tracking</div>
                    <div>🔥 Outlier Detection</div>
                  </div>
                </div>

                <div className="bg-purple-800/30 rounded-lg p-4 border border-purple-400/20">
                  <div className="text-sm font-semibold text-purple-200 mb-2 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    AI Insights
                  </div>
                  <div className="text-xs text-purple-100">
                    Smart pattern recognition and performance predictions
                  </div>
                </div>
              </div>
            </div>
            {/* Outlier Banner for Top Performers */}
            {(() => {
              // Add filter logic for top performers
              const filteredTop = (topFormatFilter && topFormatFilter !== 'All')
                ? contentSource.filter(v => getContentFormat(Number(v.duration)) === topFormatFilter)
                : contentSource;
              const allViews = filteredTop.map(vv => Number(vv.views) || 0).filter(n => n > 0);
              const avgViews = allViews.length ? allViews.reduce((a, b) => a + b, 0) / allViews.length : 0;
              const outliers = filteredTop
                .map((v, i) => ({
                  index: i,
                  title: v.title || v.channelName || v.channel || 'Untitled',
                  views: Number(v.views),
                  multiplier: avgViews > 0 ? Number(v.views) / avgViews : 0
                }))
                .filter(v => v.views >= 3 * avgViews);
              if (!outliers.length) return null;
              const mostExtreme = outliers.reduce((a, b) => (a.multiplier > b.multiplier ? a : b));
              return (
                <div className="mb-4 px-4 py-3 rounded-xl bg-red-500/90 text-white font-bold text-center shadow-lg border border-red-400/60">
                  Outlier detected: <span className="underline">{mostExtreme.title}</span> got {mostExtreme.multiplier.toFixed(1)}x more views than average!
                </div>
              );
            })()}
            {(() => {
              // Add filter logic for top performers
              const filteredTop = (topFormatFilter && topFormatFilter !== 'All')
                ? contentSource.filter(v => getContentFormat(Number(v.duration)) === topFormatFilter)
                : contentSource;

              // Sort videos based on the selected sort criteria
              const sortedVideos = [...filteredTop].sort((a, b) => {
                switch (sortBy) {
                  case 'views':
                    return Number(b.views || 0) - Number(a.views || 0);
                  case 'likes':
                    return Number(b.likes || 0) - Number(a.likes || 0);
                  case 'comments':
                    return Number(b.comments || 0) - Number(a.comments || 0);
                  case 'engagement':
                    return Number(b.engagementRate || 0) - Number(a.engagementRate || 0);
                  default:
                    return Number(b.views || 0) - Number(a.views || 0);
                }
              });

              if (sortedVideos.length === 0) {
                return (
                  <div className="flex flex-col items-center justify-center py-12 text-white/70">
                    <svg width="48" height="48" fill="none" viewBox="0 0 24 24" className="mb-2 text-yellow-300"><path fill="currentColor" d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20Zm0 3a7 7 0 1 1 0 14 7 7 0 0 1 0-14Zm0 2a5 5 0 1 0 0 10A5 5 0 0 0 12 7Zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6Z"/></svg>
                    <div className="text-lg font-semibold">No top performers found yet.</div>
                    <div className="text-sm text-white/50 mt-1">Try adjusting your filters or running a new analysis!</div>
                  </div>
                );
              }
              return (
                <>
                  {/* Performance Summary Cards */}
                  <div className="mb-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                    {(() => {
                      const allViews = filteredTop.map(v => Number(v.views) || 0).filter(n => n > 0);
                      const avgViews = allViews.length ? allViews.reduce((a, b) => a + b, 0) / allViews.length : 0;
                      const maxViews = allViews.length ? Math.max(...allViews) : 0;
                      const avgEngagement = filteredTop.length ? filteredTop.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / filteredTop.length : 0;
                      const topPerformer = filteredTop.find(v => Number(v.views) === maxViews);

                      return [
                        {
                          title: 'Total Videos',
                          value: filteredTop.length.toLocaleString(),
                          icon: '📊',
                          color: 'from-blue-600 to-blue-800',
                          bgColor: 'bg-blue-900/30',
                          borderColor: 'border-blue-400/20'
                        },
                        {
                          title: 'Avg. Views',
                          value: Math.round(avgViews).toLocaleString(),
                          icon: '👀',
                          color: 'from-green-600 to-green-800',
                          bgColor: 'bg-green-900/30',
                          borderColor: 'border-green-400/20'
                        },
                        {
                          title: 'Peak Performance',
                          value: maxViews.toLocaleString(),
                          icon: '🚀',
                          color: 'from-purple-600 to-purple-800',
                          bgColor: 'bg-purple-900/30',
                          borderColor: 'border-purple-400/20'
                        },
                        {
                          title: 'Avg. Engagement',
                          value: avgEngagement.toFixed(3),
                          icon: '💝',
                          color: 'from-pink-600 to-pink-800',
                          bgColor: 'bg-pink-900/30',
                          borderColor: 'border-pink-400/20'
                        }
                      ].map((stat, idx) => (
                        <div key={idx} className={`${stat.bgColor} rounded-lg p-4 border ${stat.borderColor} hover:scale-105 transition-all duration-300`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-2xl">{stat.icon}</span>
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${stat.color}`}></div>
                          </div>
                          <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                          <div className="text-sm text-white/70">{stat.title}</div>
                        </div>
                      ));
                    })()}
                  </div>

                  {/* Featured Top 5 Performers */}
                  <div className="mb-8">
                    <h4 className="text-xl font-bold text-purple-300 mb-4 flex items-center gap-2">
                      <span className="text-2xl">🏆</span>
                      Featured Top 5 Performers
                      <span className="text-sm text-purple-400/70 font-normal ml-2">(Based on {sortBy})</span>
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {sortedVideos.slice(0, 5).map((v, i) => {
                      // Try to get YouTube video ID from youtubeUrl
                      let videoId = '';
                      if (v.youtubeUrl) {
                        const match = String(v.youtubeUrl).match(/(?:v=|youtu\.be\/|embed\/)([\w-]{11})/);
                        if (match) videoId = match[1];
                      }
                      // Tags as array
                      let tags: string[] = [];
                      if (Array.isArray(v.tags)) tags = v.tags;
                      else if (typeof v.tags === 'string') {
                        try {
                          const parsed = JSON.parse(v.tags);
                          if (Array.isArray(parsed)) tags = parsed;
                          else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                        } catch {
                          tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                        }
                      }
                      // Enhanced performance metrics
                      const allViews = contentSource.map(vv => Number(vv.views) || 0).filter(n => n > 0);
                      const avgViews = allViews.length ? allViews.reduce((a, b) => a + b, 0) / allViews.length : 0;
                      const maxViews = allViews.length ? Math.max(...allViews) : 0;
                      const isTopVideo = Number(v.views) === maxViews;
                      const isOutlier = avgViews > 0 && Number(v.views) >= 3 * avgViews;

                      // Enhanced ranking system
                      const rank = i + 1;
                      const multiplier = avgViews > 0 ? Number(v.views) / avgViews : 0;
                      const engagementRate = Number(v.engagementRate) || 0;
                      const viewsPerHour = Number(v.viewsPerHour) || 0;

                      // Dynamic emoji and styling based on performance
                      let emoji = '🏆';
                      let rankColor = 'from-yellow-400 to-yellow-600';
                      let cardGlow = '';

                      if (multiplier >= 5) {
                        emoji = '🔥';
                        rankColor = 'from-red-400 to-red-600';
                        cardGlow = 'shadow-red-500/20 shadow-2xl';
                      } else if (multiplier >= 3) {
                        emoji = '⚡';
                        rankColor = 'from-orange-400 to-orange-600';
                        cardGlow = 'shadow-orange-500/20 shadow-xl';
                      } else if (multiplier >= 2) {
                        emoji = '✨';
                        rankColor = 'from-yellow-400 to-yellow-600';
                        cardGlow = 'shadow-yellow-500/20 shadow-lg';
                      }

                      // Format detection with enhanced icons
                      const format = getContentFormat(Number(v.duration));
                      const formatData = {
                        'Shorts': { icon: '⚡', color: 'text-red-400', bg: 'bg-red-900/30' },
                        'Short-form': { icon: '🎯', color: 'text-blue-400', bg: 'bg-blue-900/30' },
                        'Mid-form': { icon: '📺', color: 'text-green-400', bg: 'bg-green-900/30' },
                        'Long-form': { icon: '🎥', color: 'text-purple-400', bg: 'bg-purple-900/30' }
                      };
                      const formatInfo = formatData[format] || formatData['Short-form'];

                      return (
                        <div
                          key={i}
                          className={`bg-gradient-to-br from-white/10 to-white/5 rounded-xl p-6 flex flex-col gap-4 border transition-all duration-300 cursor-pointer relative hover:scale-105 hover:rotate-1 ${
                            isTopVideo
                              ? `ring-4 ring-yellow-400/80 border-yellow-400/60 ${cardGlow} scale-[1.02]`
                              : 'border-white/20 hover:border-purple-400/40'
                          }`}
                          onClick={() => { setModalIndex(i); setShowVideo(null); }}
                        >
                          {/* Header with Rank and Performance Badge */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`bg-gradient-to-r ${rankColor} rounded-full p-3 shadow-lg`}>
                                <span className="text-2xl font-bold text-white">#{rank}</span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-lg font-bold text-white">{emoji} {multiplier.toFixed(1)}x</span>
                                <span className="text-xs text-white/60">vs average</span>
                              </div>
                            </div>

                            {/* Format Badge */}
                            <div className={`${formatInfo.bg} rounded-lg px-3 py-2 border border-white/20`}>
                              <div className="flex items-center gap-2">
                                <span className="text-lg">{formatInfo.icon}</span>
                                <span className={`text-sm font-bold ${formatInfo.color}`}>{format}</span>
                              </div>
                            </div>
                          </div>

                          {/* Channel and Title */}
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-lg font-bold text-purple-300">
                                {v.channelName || v.channel || 'Channel'}
                              </span>
                              {isTopVideo && (
                                <span className="px-2 py-1 rounded-full bg-yellow-400/80 text-black text-xs font-bold shadow animate-pulse">
                                  👑 CHAMPION
                                </span>
                              )}
                              {isOutlier && !isTopVideo && (
                                <span className="px-2 py-1 rounded-full bg-red-500/80 text-white text-xs font-bold shadow">
                                  🔥 VIRAL
                                </span>
                              )}
                            </div>
                            <div className="text-white/90 font-semibold line-clamp-2">
                              {v.title || 'Sample Video Title'}
                            </div>
                          </div>

                          {/* Performance Metrics Grid */}
                          <div className="grid grid-cols-2 gap-3">
                            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-blue-400">👀</span>
                                <span className="text-xs text-white/60">Views</span>
                              </div>
                              <div className="text-lg font-bold text-white">{Number(v.views).toLocaleString()}</div>
                            </div>
                            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-pink-400">💝</span>
                                <span className="text-xs text-white/60">Engagement</span>
                              </div>
                              <div className="text-lg font-bold text-white">{engagementRate.toFixed(3)}</div>
                            </div>
                            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-green-400">⚡</span>
                                <span className="text-xs text-white/60">Views/Hour</span>
                              </div>
                              <div className="text-lg font-bold text-white">{Math.round(viewsPerHour).toLocaleString()}</div>
                            </div>
                            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-yellow-400">⏱️</span>
                                <span className="text-xs text-white/60">Duration</span>
                              </div>
                              <div className="text-lg font-bold text-white">{Math.round(Number(v.duration) / 60)}m</div>
                            </div>
                          </div>

                          {/* Special Effects for Top Performers */}
                          {isTopVideo && (
                            <div className="absolute -top-2 -right-2 animate-bounce">
                              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-2 shadow-lg">
                                <span className="text-xl">👑</span>
                              </div>
                            </div>
                          )}

                          {/* Delete Button */}
                          {v.youtubeUrl && (
                            <button
                              className="absolute top-3 right-3 p-2 rounded-lg hover:bg-red-500/20 transition-colors text-gray-400 hover:text-red-400 opacity-0 group-hover:opacity-100"
                              title="Delete video"
                              onClick={e => { e.stopPropagation(); handleRemoveVideo(v.youtubeUrl); }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* All Videos Section */}
                  <div className="mt-12">
                    <h4 className="text-xl font-bold text-purple-300 mb-4 flex items-center gap-2">
                      <span className="text-2xl">📊</span>
                      All Videos
                      <span className="text-sm text-purple-400/70 font-normal ml-2">({sortedVideos.length} total)</span>
                    </h4>

                    {/* Video List */}
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {sortedVideos.map((v, i) => {
                        const isTopPerformer = i < 5; // Top 5 are featured
                        const avgViews = sortedVideos.length > 0 ? sortedVideos.reduce((sum, vid) => sum + (Number(vid.views) || 0), 0) / sortedVideos.length : 0;
                        const multiplier = avgViews > 0 ? Number(v.views) / avgViews : 0;

                        return (
                          <div
                            key={v.youtubeUrl || i}
                            className={`relative p-4 rounded-lg border transition-all duration-300 hover:scale-[1.02] cursor-pointer group ${
                              isTopPerformer
                                ? 'bg-gradient-to-r from-yellow-900/30 to-orange-900/30 border-yellow-400/40 shadow-lg'
                                : 'bg-purple-800/20 border-purple-400/20 hover:border-purple-400/40'
                            }`}
                            onClick={() => setModalIndex(contentSource.findIndex(vid => vid.youtubeUrl === v.youtubeUrl))}
                          >
                            {/* Rank Badge */}
                            <div className={`absolute -top-2 -left-2 w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold ${
                              isTopPerformer
                                ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black'
                                : 'bg-purple-600 text-white'
                            }`}>
                              #{i + 1}
                            </div>

                            {/* Top Performer Crown */}
                            {isTopPerformer && (
                              <div className="absolute -top-2 -right-2 animate-bounce">
                                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-1 shadow-lg">
                                  <span className="text-sm">👑</span>
                                </div>
                              </div>
                            )}

                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <h5 className={`font-semibold truncate ${isTopPerformer ? 'text-yellow-200' : 'text-white'}`}>
                                  {v.title || v.channelName || v.channel || 'Untitled'}
                                </h5>
                                <p className="text-sm text-white/60 truncate">
                                  {v.channelName || v.channel || 'Unknown Channel'}
                                </p>
                              </div>

                              <div className="flex items-center gap-4 text-sm">
                                <div className="text-center">
                                  <div className={`font-bold ${isTopPerformer ? 'text-yellow-300' : 'text-white'}`}>
                                    {Number(v.views || 0).toLocaleString()}
                                  </div>
                                  <div className="text-xs text-white/60">Views</div>
                                </div>

                                <div className="text-center">
                                  <div className={`font-bold ${isTopPerformer ? 'text-yellow-300' : 'text-white'}`}>
                                    {Number(v.engagementRate || 0).toFixed(3)}
                                  </div>
                                  <div className="text-xs text-white/60">Engagement</div>
                                </div>

                                {/* Performance Indicator */}
                                {multiplier >= 3 && (
                                  <div className="flex items-center gap-1 bg-red-500/20 text-red-300 px-2 py-1 rounded-full text-xs font-bold">
                                    🔥 {multiplier.toFixed(1)}x
                                  </div>
                                )}
                                {multiplier >= 2 && multiplier < 3 && (
                                  <div className="flex items-center gap-1 bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full text-xs font-bold">
                                    ✨ {multiplier.toFixed(1)}x
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Delete Button */}
                            {v.youtubeUrl && (
                              <button
                                className="absolute top-3 right-3 p-2 rounded-lg hover:bg-red-500/20 transition-colors text-gray-400 hover:text-red-400 opacity-0 group-hover:opacity-100"
                                title="Delete video"
                                onClick={e => { e.stopPropagation(); handleRemoveVideo(v.youtubeUrl); }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            })()}
            {
              // Modal variables
              (modalIndex !== null && (() => {
                const v = contentSource[modalIndex];
                let videoId = '';
                if (v.youtubeUrl) {
                  const match = String(v.youtubeUrl).match(/(?:v=|youtu\.be\/|embed\/)([\w-]{11})/);
                  if (match) videoId = match[1];
                }
                let tags: string[] = [];
                if (Array.isArray(v.tags)) tags = v.tags;
                else if (typeof v.tags === 'string') {
                  try {
                    const parsed = JSON.parse(v.tags);
                    if (Array.isArray(parsed)) tags = parsed;
                    else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  } catch {
                    tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  }
                }
                // Helper to format percentages to 4 digits after the point
                const formatPercent = (val: any) => {
                  const num = Number(val);
                  if (isNaN(num)) return val;
                  return num.toFixed(4) + '%';
                };
                // Helper to detect if video is vertical (shorts)
                const isVertical = v.youtubeUrl && (v.youtubeUrl.includes('shorts') || (v.width && v.height && v.height > v.width));
                const modalMaxW = isVertical ? 'max-w-lg' : 'max-w-2xl';
                const videoMaxW = isVertical ? 400 : 900;
                return ReactDOM.createPortal(
                  <div className="fixed inset-0 z-50 flex items-center justify-center">
                    {/* Overlay with strong blur and dark background */}
                    <div
                      className="absolute inset-0 bg-black/80 backdrop-blur-[8px] transition-all duration-300"
                      onClick={() => setModalIndex(null)}
                    />
                    <div
                      className={`relative bg-gradient-to-br from-[#3a2c1a]/95 to-[#2d1a1a]/95 rounded-2xl px-6 py-8 ${modalMaxW} w-full shadow-2xl border border-white/20 animate-fade-in flex flex-col items-center overflow-y-auto max-h-[90vh]`}
                      style={{ minWidth: 320, maxWidth: videoMaxW }}
                      onClick={e => e.stopPropagation()}
                    >
                      <button
                        className="absolute top-3 right-3 text-white/80 hover:text-yellow-300 text-2xl font-bold z-10"
                        onClick={() => setModalIndex(null)}
                        aria-label="Close"
                      >
                        ×
                      </button>
                      <div className="text-lg font-bold text-yellow-300 mb-1 w-full text-left">{v.channelName || v.channel || 'Channel'}</div>
                      <div className="text-white/90 font-semibold mb-2 w-full text-left">{v.title || 'Sample Video Title'}</div>
                      <div className="flex gap-6 text-white/90 text-base mb-4 w-full flex-wrap items-center justify-start">
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#facc15" d="M12 4.5c-7 0-10 7.5-10 7.5s3 7.5 10 7.5 10-7.5 10-7.5-3-7.5-10-7.5Zm0 13c-4.97 0-7.74-4.13-8.7-5.5C4.26 10.13 7.03 6 12 6s7.74 4.13 8.7 5.5c-.96 1.37-3.73 5.5-8.7 5.5Zm0-9A3.5 3.5 0 1 0 12 15a3.5 3.5 0 0 0 0-7Zm0 5A1.5 1.5 0 1 1 12 9a1.5 1.5 0 0 1 0 3Z"/></svg>
                          {v.views !== undefined && v.views !== null ? Number(v.views).toLocaleString() : '-'}
                        </span>
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#f87171" d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 1.01 4.5 2.09C13.09 4.01 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35Z"/></svg>
                          {v.likes !== undefined && v.likes !== null ? Number(v.likes).toLocaleString() : '-'}
                        </span>
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#60a5fa" d="M21 6.5a2.5 2.5 0 0 0-2.5-2.5h-13A2.5 2.5 0 0 0 3 6.5v11A2.5 2.5 0 0 0 5.5 20h13a2.5 2.5 0 0 0 2.5-2.5v-11Zm-2.5-1a1 1 0 0 1 1 1V7h-15v-.5a1 1 0 0 1 1-1h13ZM20 8v9.5a1 1 0 0 1-1 1h-13a1 1 0 0 1-1-1V8h15Zm-7.5 2.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm-5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm10 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/></svg>
                          {v.comments !== undefined && v.comments !== null ? Number(v.comments).toLocaleString() : '-'}
                        </span>
                      </div>
                      {/* YouTube embed or thumbnail */}
                      {videoId ? (
                        <div className="mb-3 flex flex-col items-center w-full justify-center">
                          {showVideo === modalIndex ? (
                            <div className="w-full flex justify-center">
                              <iframe
                                style={{ width: '100%', height: isVertical ? '600px' : '480px', minHeight: isVertical ? '500px' : '360px', maxWidth: isVertical ? '400px' : '900px', borderRadius: '1rem', boxShadow: '0 4px 32px 0 #0008' }}
                                src={`https://www.youtube.com/embed/${videoId}`}
                                title="YouTube video player"
                                frameBorder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                allowFullScreen
                              ></iframe>
                            </div>
                          ) : (
                            <div className="w-full flex flex-col items-center justify-center">
                              <img
                                src={`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`}
                                alt="Video thumbnail"
                                className="rounded-lg shadow-lg border border-white/20 cursor-pointer hover:brightness-110 transition"
                                onClick={e => { e.stopPropagation(); setShowVideo(modalIndex); }}
                                style={{ width: '100%', maxWidth: isVertical ? '400px' : '900px', minHeight: isVertical ? '500px' : '360px', objectFit: 'cover' }}
                              />
                              <button
                                className="mt-2 px-4 py-1 rounded bg-gradient-to-r from-yellow-400 to-red-400 text-white font-bold shadow hover:scale-105 transition"
                                onClick={e => { e.stopPropagation(); setShowVideo(modalIndex); }}
                              >
                                ▶ Watch in Card
                              </button>
                            </div>
                          )}
                        </div>
                      ) : null}
                      {/* Tags */}
                      {tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-2 w-full justify-center">
                          {tags.map((tag, idx) => (
                            <span key={idx} className="px-2 py-1 rounded-full bg-yellow-400/20 text-yellow-300 text-xs font-bold shadow border border-yellow-300/30 mb-1">{tag}</span>
                          ))}
                        </div>
                      )}
                      {/* Description */}
                      {v.description && (
                        <div className="text-white/80 text-sm bg-white/10 rounded p-3 border border-white/10 shadow-inner mb-2 w-full">
                          {v.description}
                        </div>
                      )}
                      {/* Other values */}
                      <div className="flex flex-wrap gap-4 text-white/70 text-xs mt-2 w-full justify-center">
                        {v.publishedDate && <span>Published: {new Date(v.publishedDate).toLocaleDateString()}</span>}
                        {v.engagementRate && <span>Engagement: {formatPercent(v.engagementRate)}</span>}
                        {v.viewsPerHour !== undefined && v.viewsPerHour !== null && <span>Views/Hour: {Math.floor(Number(v.viewsPerHour)).toLocaleString()}</span>}
                        {v.likeRate && <span>Like Rate: {formatPercent(v.likeRate)}</span>}
                        {v.commentRate && <span>Comment Rate: {formatPercent(v.commentRate)}</span>}
                        {v.duration && <span>Duration: {v.duration} sec</span>}
                      </div>
                    </div>
                  </div>,
                  document.body
                );
              })())
            }
          </>
        )}
      {/* Undo Toast Popup */}
      {lastDeletedVideo && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end animate-slide-up">
          <div className="bg-yellow-900/95 border border-yellow-400/40 rounded-xl shadow-lg px-6 py-4 flex items-center gap-4 min-w-[260px] max-w-xs">
            <div className="flex-1">
              <div className="font-bold text-yellow-200 mb-1">Video deleted</div>
              <button
                className="text-yellow-300 underline font-semibold hover:text-yellow-400 transition"
                onClick={handleUndoDelete}
              >
                Undo ({undoTimer}s)
              </button>
            </div>
            <Trash2 className="w-6 h-6 text-yellow-400" />
          </div>
          <div className="w-full h-1 bg-yellow-400/30 rounded-b-xl overflow-hidden mt-1">
            <div
              className="h-1 bg-yellow-400 transition-all duration-1000"
              style={{ width: `${(undoTimer / 10) * 100}%` }}
            />
          </div>
        </div>
      )}
      {activeTab === 3 && (
        <div className="space-y-8">
          {/* Video Comparison (A/B Testing) */}
          <ABTestPanel />

          {/* Channel Comparison */}
          <div className="bg-gradient-to-br from-slate-900/50 to-gray-900/50 border border-yellow-500/30 rounded-xl p-6 shadow-2xl text-white backdrop-blur-sm">
            <h2 className="text-3xl font-bold text-yellow-300 mb-6 text-center flex items-center justify-center gap-3">
              <svg className="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              Channel Performance Comparison
            </h2>

            {/* Channel Comparison Section */}
            <div className="mb-8">
              <h3 className="text-xl font-bold text-yellow-300 mb-4">Compare Channels</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="bg-gradient-to-br from-blue-900/20 to-indigo-900/20 p-4 rounded-lg border border-blue-400/30">
                  <label className="block text-sm mb-2 font-semibold text-blue-200 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011 1v8a1 1 0 01-1 1H8a1 1 0 01-1-1V2a1 1 0 011-1z" />
                    </svg>
                    Channel A
                  </label>
                  <select
                    className="w-full bg-[#1a1a2e] border border-blue-400/30 rounded-lg px-3 py-3 text-white/90 focus:outline-none focus:border-blue-400 transition-all duration-300 hover:border-blue-400/50"
                    value={selectedChannelA || ''}
                    onChange={(e) => setSelectedChannelA(e.target.value)}
                  >
                    <option value="">-- Select a channel --</option>
                    {uniqueChannels.map((channel) => (
                      <option key={channel} value={channel}>
                        {channel}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 p-4 rounded-lg border border-purple-400/30">
                  <label className="block text-sm mb-2 font-semibold text-purple-200 flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011 1v8a1 1 0 01-1 1H8a1 1 0 01-1-1V2a1 1 0 011-1z" />
                    </svg>
                    Channel B
                  </label>
                  <select
                    className="w-full bg-[#1a1a2e] border border-purple-400/30 rounded-lg px-3 py-3 text-white/90 focus:outline-none focus:border-purple-400 transition-all duration-300 hover:border-purple-400/50"
                    value={selectedChannelB || ''}
                    onChange={(e) => setSelectedChannelB(e.target.value)}
                  >
                    <option value="">-- Select a channel --</option>
                    {uniqueChannels.map((channel) => (
                      <option key={channel} value={channel}>
                        {channel}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Channel Comparison Results */}
              {selectedChannelA && selectedChannelB && (
                <div className="bg-gradient-to-br from-gray-900/50 to-slate-900/50 rounded-xl border border-yellow-400/20 shadow-lg p-6">
                  <h4 className="text-lg font-bold text-yellow-300 mb-4">Channel Performance Comparison</h4>
                  {(() => {
                    const channelAVideos = data.filter(v => (v.channelName || v.channel) === selectedChannelA);
                    const channelBVideos = data.filter(v => (v.channelName || v.channel) === selectedChannelB);

                    const channelAStats = {
                      totalVideos: channelAVideos.length,
                      totalViews: channelAVideos.reduce((sum, v) => sum + (Number(v.views) || 0), 0),
                      avgViews: channelAVideos.length > 0 ? channelAVideos.reduce((sum, v) => sum + (Number(v.views) || 0), 0) / channelAVideos.length : 0,
                      avgEngagement: channelAVideos.length > 0 ? channelAVideos.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / channelAVideos.length : 0,
                      avgDuration: channelAVideos.length > 0 ? channelAVideos.reduce((sum, v) => sum + (Number(v.duration) || 0), 0) / channelAVideos.length : 0,
                    };

                    const channelBStats = {
                      totalVideos: channelBVideos.length,
                      totalViews: channelBVideos.reduce((sum, v) => sum + (Number(v.views) || 0), 0),
                      avgViews: channelBVideos.length > 0 ? channelBVideos.reduce((sum, v) => sum + (Number(v.views) || 0), 0) / channelBVideos.length : 0,
                      avgEngagement: channelBVideos.length > 0 ? channelBVideos.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / channelBVideos.length : 0,
                      avgDuration: channelBVideos.length > 0 ? channelBVideos.reduce((sum, v) => sum + (Number(v.duration) || 0), 0) / channelBVideos.length : 0,
                    };

                    const metrics = [
                      { label: 'Total Videos', keyA: 'totalVideos', keyB: 'totalVideos', formatter: (val: number) => val.toLocaleString() },
                      { label: 'Total Views', keyA: 'totalViews', keyB: 'totalViews', formatter: (val: number) => val.toLocaleString() },
                      { label: 'Avg. Views per Video', keyA: 'avgViews', keyB: 'avgViews', formatter: (val: number) => Math.round(val).toLocaleString() },
                      { label: 'Avg. Engagement Rate', keyA: 'avgEngagement', keyB: 'avgEngagement', formatter: (val: number) => `${val.toFixed(2)}%` },
                      { label: 'Avg. Duration (min)', keyA: 'avgDuration', keyB: 'avgDuration', formatter: (val: number) => `${(val / 60).toFixed(1)}` },
                    ];

                    return (
                      <div className="overflow-x-auto">
                        <table className="min-w-full text-sm">
                          <thead className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20">
                            <tr>
                              <th className="py-4 px-4 text-left font-bold text-yellow-200">Metric</th>
                              <th className="py-4 px-4 text-left font-bold text-blue-300 w-48 bg-blue-900/20">{selectedChannelA}</th>
                              <th className="py-4 px-4 text-left font-bold text-purple-300 w-48 bg-purple-900/20">{selectedChannelB}</th>
                            </tr>
                          </thead>
                          <tbody>
                            {metrics.map(({ label, keyA, keyB, formatter }) => {
                              const aVal = channelAStats[keyA as keyof typeof channelAStats];
                              const bVal = channelBStats[keyB as keyof typeof channelBStats];
                              const winner = aVal > bVal ? 'A' : bVal > aVal ? 'B' : 'tie';

                              return (
                                <tr key={label} className="border-t border-yellow-400/10 hover:bg-yellow-400/5 transition-all duration-300">
                                  <td className="py-3 px-4 font-semibold text-yellow-200">{label}</td>
                                  <td className={`py-3 px-4 font-bold transition-all duration-300 ${
                                    winner === 'A'
                                      ? 'bg-green-600/30 text-green-200 border-l-4 border-green-400'
                                      : 'text-blue-200 hover:bg-blue-900/10'
                                  }`}>
                                    {winner === 'A' && <span className="mr-2">🏆</span>}
                                    {formatter(aVal)}
                                  </td>
                                  <td className={`py-3 px-4 font-bold transition-all duration-300 ${
                                    winner === 'B'
                                      ? 'bg-green-600/30 text-green-200 border-l-4 border-green-400'
                                      : 'text-purple-200 hover:bg-purple-900/10'
                                  }`}>
                                    {winner === 'B' && <span className="mr-2">🏆</span>}
                                    {formatter(bVal)}
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {activeTab === 4 && (
        <div className="space-y-8">
          {/* Advanced Analytics */}
          <div className="bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-xl p-6 border border-indigo-400/20 shadow-lg">
            <h3 className="text-xl font-bold text-indigo-300 mb-6 flex items-center gap-2">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
              </svg>
              Advanced Analytics Dashboard
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Engagement Rate Breakdown */}
              <div className="bg-indigo-900/20 p-5 rounded-lg border border-indigo-400/20">
                <h4 className="text-lg font-semibold text-indigo-200 mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  Engagement Breakdown
                </h4>
                {(() => {
                  const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
                  const totalLikes = contentSource.reduce((sum, v) => sum + (Number(v.likes) || 0), 0);
                  const totalComments = contentSource.reduce((sum, v) => sum + (Number(v.comments) || 0), 0);
                  const totalEngagement = totalLikes + totalComments;
                  const likeRatio = totalEngagement > 0 ? (totalLikes / totalEngagement * 100) : 0;
                  const commentRatio = totalEngagement > 0 ? (totalComments / totalEngagement * 100) : 0;

                  return (
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-white/80">Likes</span>
                        <span className="text-sm font-bold text-indigo-200">{likeRatio.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-indigo-400 h-2 rounded-full transition-all duration-500" style={{ width: `${likeRatio}%` }}></div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-white/80">Comments</span>
                        <span className="text-sm font-bold text-indigo-200">{commentRatio.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-purple-400 h-2 rounded-full transition-all duration-500" style={{ width: `${commentRatio}%` }}></div>
                      </div>
                      <div className="text-xs text-indigo-200 mt-3 p-2 bg-indigo-900/30 rounded">
                        <strong>Insight:</strong> {likeRatio > commentRatio ? "Audience prefers liking over commenting" : "High comment engagement indicates strong community interaction"}
                      </div>
                    </div>
                  );
                })()}
              </div>

              {/* Audience Retention Predictor */}
              <div className="bg-purple-900/20 p-5 rounded-lg border border-purple-400/20">
                <h4 className="text-lg font-semibold text-purple-200 mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  Retention Predictor
                </h4>
                {(() => {
                  const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
                  const avgDuration = contentSource.length > 0 ? contentSource.reduce((sum, v) => sum + (Number(v.duration) || 0), 0) / contentSource.length : 0;
                  const avgEngagement = contentSource.length > 0 ? contentSource.reduce((sum, v) => sum + (Number(v.engagementRate) || 0), 0) / contentSource.length : 0;
                  const retentionScore = Math.min(100, (avgEngagement * 100 * 2) + (avgDuration > 300 ? 20 : avgDuration > 60 ? 10 : 5));

                  return (
                    <div className="space-y-3">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-300">{retentionScore.toFixed(0)}%</div>
                        <div className="text-sm text-white/80">Predicted Retention</div>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-3">
                        <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000" style={{ width: `${retentionScore}%` }}></div>
                      </div>
                      <div className="text-xs text-purple-200 p-2 bg-purple-900/30 rounded">
                        <strong>Prediction:</strong> {
                          retentionScore > 70 ? "High retention expected - content keeps viewers engaged" :
                          retentionScore > 40 ? "Moderate retention - consider improving hooks" :
                          "Low retention risk - focus on stronger openings"
                        }
                      </div>
                    </div>
                  );
                })()}
              </div>

              {/* Revenue Impact Calculator */}
              <div className="bg-green-900/20 p-5 rounded-lg border border-green-400/20">
                <h4 className="text-lg font-semibold text-green-200 mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  Revenue Impact
                </h4>
                {(() => {
                  const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
                  const totalViews = contentSource.reduce((sum, v) => sum + (Number(v.views) || 0), 0);
                  const avgViewsPerHour = contentSource.length > 0 ? contentSource.reduce((sum, v) => sum + (Number(v.viewsPerHour) || 0), 0) / contentSource.length : 0;
                  // Rough estimate: $1-3 per 1000 views
                  const estimatedRevenue = (totalViews / 1000) * 2;
                  const potentialIncrease = avgViewsPerHour > 100 ? 25 : avgViewsPerHour > 50 ? 15 : 10;

                  return (
                    <div className="space-y-3">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-300">${estimatedRevenue.toFixed(0)}</div>
                        <div className="text-sm text-white/80">Estimated Revenue</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-400">+{potentialIncrease}%</div>
                        <div className="text-xs text-white/80">Potential Increase</div>
                      </div>
                      <div className="text-xs text-green-200 p-2 bg-green-900/30 rounded">
                        <strong>Strategy:</strong> {
                          avgViewsPerHour > 100 ? "Focus on viral content patterns" :
                          "Optimize for higher view velocity to increase revenue potential"
                        }
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>

            {/* Content Saturation Detector */}
            <div className="mt-6 bg-yellow-900/20 p-5 rounded-lg border border-yellow-400/20">
              <h4 className="text-lg font-semibold text-yellow-200 mb-3 flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                Content Saturation Analysis
              </h4>
              {(() => {
                const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
                // Analyze tag frequency to detect saturation
                const tagCounts: Record<string, number> = {};
                contentSource.forEach(v => {
                  const tags = Array.isArray(v.tags) ? v.tags :
                    typeof v.tags === 'string' ? v.tags.split(',').map(t => t.trim()) : [];
                  tags.forEach(tag => {
                    if (tag) tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                  });
                });

                const sortedTags = Object.entries(tagCounts).sort((a, b) => b[1] - a[1]).slice(0, 5);
                const totalVideos = contentSource.length;

                return (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-semibold text-yellow-200 mb-2">Most Used Tags</div>
                      <div className="space-y-2">
                        {sortedTags.map(([tag, count]) => {
                          const saturationLevel = totalVideos > 0 ? (count / totalVideos) * 100 : 0;
                          return (
                            <div key={tag} className="flex justify-between items-center">
                              <span className="text-xs text-white/80 truncate">{tag}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-yellow-200">{count} videos</span>
                                <span className={`text-xs px-2 py-1 rounded ${
                                  saturationLevel > 50 ? 'bg-red-500/20 text-red-300' :
                                  saturationLevel > 30 ? 'bg-yellow-500/20 text-yellow-300' :
                                  'bg-green-500/20 text-green-300'
                                }`}>
                                  {saturationLevel.toFixed(0)}%
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-yellow-200 mb-2">Saturation Insights</div>
                      <div className="text-xs text-yellow-200 space-y-2">
                        {sortedTags.some(([, count]) => totalVideos > 0 && (count / totalVideos) > 0.5) ? (
                          <div className="p-2 bg-red-900/30 rounded">
                            <strong>High Saturation:</strong> Some topics are overused. Consider diversifying content themes.
                          </div>
                        ) : (
                          <div className="p-2 bg-green-900/30 rounded">
                            <strong>Balanced Content:</strong> Good topic diversity maintained.
                          </div>
                        )}
                        <div className="p-2 bg-yellow-900/30 rounded">
                          <strong>Recommendation:</strong> Explore trending topics outside your top 5 tags for fresh content opportunities.
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
      </div>
      {/* Add global scrollbar styles for yellow/orange palette */}
      <style>{`
        /* For all scrollbars */
        ::-webkit-scrollbar {
          height: 10px;
          width: 10px;
          background: #2d1a1a;
          border-radius: 8px;
        }
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(90deg, #facc15 0%, #f59e42 100%);
          border-radius: 8px;
          border: 2px solid #2d1a1a;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(90deg, #f59e42 0%, #facc15 100%);
        }
        /* For horizontal tag scrolls */
        .tag-scroll::-webkit-scrollbar {
          height: 12px;
          background: #3a2c1a;
        }
        .tag-scroll::-webkit-scrollbar-thumb {
          background: linear-gradient(90deg, #facc15 0%, #f59e42 100%);
          border-radius: 8px;
          border: 2px solid #3a2c1a;
        }
      `}</style>
      </div>
    </div>
  );
};

export default AnalysisDashboard; 